# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup

import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
#using(r"config/farm")

from close import *
#from userfarm import *

def ahgar():
    try:
        if exists(out_kal3ah):
            aout_2_in()

        open_menu_forsan()
        
        if exists(Template(r"tpl1666771048904.png", record_pos=(-0.02, 0.208), resolution=(1080, 1920))):
            touch(Template(r"tpl1666771048904.png", record_pos=(-0.02, 0.208), resolution=(1080, 1920)))
        if exists(Template(r"tpl1656657624101.png", threshold=0.85, record_pos=(0.13, 0.37), resolution=(1080, 1920))):
            touch((534, 1385))
            sleep()

            if exists(Template(r"tpl1656657704915.png", threshold=0.95, record_pos=(-0.344, -0.269), resolution=(1080, 1920))):
                swipe(Template(r"tpl1656657704915.png", threshold=0.95, record_pos=(-0.344, -0.269), resolution=(1080, 1920)), vector=[0.351, 0.0949])
                sleep(3)
                swipe(Template(r"tpl1656657704915.png", threshold=0.95, record_pos=(-0.344, -0.269), resolution=(1080, 1920)), vector=[0.351, 0.0949])
                sleep(2)
                touch(back_fede_shafaf)

    except:
        print('axcept ahgar1')
        youm_alhath()

def ahgar_2():
    if exists(out_kal3ah):
        aout_2_in()

    try:
        open_menu_forsan()

        if exists(Template(r"tpl1656657624101.png", threshold=0.85, record_pos=(0.13, 0.37), resolution=(1080, 1920))):
            touch((534, 1385))
            sleep(2)
            if exists(Template(r"tpl1658428067950.png", threshold=0.9, record_pos=(-0.142, -0.481), resolution=(1080, 1920))):
                swipe(Template(r"tpl1658428067950.png", threshold=0.9, record_pos=(-0.142, -0.481), resolution=(1080, 1920)), vector=[0.1513, 0.1777])
                sleep(3)
                swipe(Template(r"tpl1658428067950.png", threshold=0.9, record_pos=(-0.142, -0.481), resolution=(1080, 1920)), vector=[0.1513, 0.1777])
                sleep(2)
                touch(back_fede_shafaf)

    except:
        print('axcept ahgar2')
        youm_alhath()

def ahgar_3():
    if exists(out_kal3ah):
        aout_2_in()

    try:
        open_menu_forsan()

        if exists(Template(r"tpl1656657624101.png", threshold=0.85, record_pos=(0.13, 0.37), resolution=(1080, 1920))):
            touch((534, 1385))
            sleep(2)
            if exists(Template(r"tpl1658428336617.png", threshold=0.9, record_pos=(0.138, -0.478), resolution=(1080, 1920))):
                swipe(Template(r"tpl1658428336617.png", threshold=0.9, record_pos=(0.138, -0.478), resolution=(1080, 1920)), vector=[-0.1327, 0.1781])
                sleep(3)
                swipe(Template(r"tpl1658428336617.png", threshold=0.9, record_pos=(0.138, -0.478), resolution=(1080, 1920)), vector=[-0.1327, 0.1781])

                sleep(2)
                touch(back_fede_shafaf)

    except:
        print('exept ahgar3')
        youm_alhath()

def ahgar_4():
    if exists(out_kal3ah):
        aout_2_in()

    try:
        open_menu_forsan()

        if exists(Template(r"tpl1656657624101.png", threshold=0.85, record_pos=(0.13, 0.37), resolution=(1080, 1920))):
            touch((534, 1385))
            sleep(2)
            if exists(Template(r"tpl1658428457512.png", threshold=0.9, record_pos=(0.34, -0.267), resolution=(1080, 1920))):
                swipe(Template(r"tpl1658428457512.png", threshold=0.9, record_pos=(0.34, -0.267), resolution=(1080, 1920)), vector=[-0.3367, 0.0593])
                sleep(3)
                swipe(Template(r"tpl1658428457512.png", threshold=0.9, record_pos=(0.34, -0.267), resolution=(1080, 1920)), vector=[-0.3367, 0.0593])


                sleep(2)
                touch(back_fede_shafaf)

    except:
        print('exept ahjar4')
        youm_alhath()


