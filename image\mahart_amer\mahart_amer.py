# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
def mahart_amer():

    try:
        if exists(back_shahen):
            touch(back_shahen)
        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        if exists(Template(r"tpl1656605501775.png", threshold=0.95, record_pos=(-0.421, 0.586), resolution=(1080, 1920))):
            touch(Template(r"tpl1656605501775.png", threshold=0.95, record_pos=(-0.421, 0.586), resolution=(1080, 1920)))
            sleep(1)
            if exists(Template(r"tpl1675446526875.png", record_pos=(-0.386, 0.239), resolution=(1080, 1920))):
                touch(Template(r"tpl1675446526875.png", record_pos=(-0.386, 0.239), resolution=(1080, 1920)))
                wait(Template(r"tpl1656605787125.png", threshold=0.95, record_pos=(0.002, -0.849), resolution=(1080, 1920)))
                if exists(Template(r"tpl1656704414786.png", threshold=0.98, record_pos=(-0.199, 0.602), resolution=(1080, 1920))):
                    touch(Template(r"tpl1656704414786.png", threshold=0.95, record_pos=(-0.199, 0.602), resolution=(1080, 1920)))
                    sleep(1)
                    touch(Template(r"tpl1656606327465.png", threshold=0.95, record_pos=(-0.001, 0.293), resolution=(1080, 1920)))
                else:
                    touch([67, 48])
                    touch([538, 1413])
        if exists(Template(r"tpl1656605501775.png", threshold=0.95, record_pos=(-0.421, 0.586), resolution=(1080, 1920))):
            touch(Template(r"tpl1656605501775.png", threshold=0.95, record_pos=(-0.421, 0.586), resolution=(1080, 1920)))
            sleep()
            if exists(Template(r"tpl1675446526875.png", record_pos=(-0.386, 0.239), resolution=(1080, 1920))):
                touch(Template(r"tpl1675446526875.png", record_pos=(-0.386, 0.239), resolution=(1080, 1920)))
                wait(Template(r"tpl1656605787125.png", threshold=0.95, record_pos=(0.002, -0.849), resolution=(1080, 1920)))
                if exists(Template(r"tpl1656606213425.png", threshold=0.98, record_pos=(-0.388, 0.641), resolution=(1080, 1920))):
                    touch(Template(r"tpl1656606213425.png", threshold=0.95, record_pos=(-0.388, 0.641), resolution=(1080, 1920)))
                    sleep(1)
                    touch(Template(r"tpl1656606327465.png", threshold=0.95, record_pos=(-0.001, 0.293), resolution=(1080, 1920)))
                    sleep(2)

                    if exists(Template(r"tpl1657523566700.png", threshold=0.95, record_pos=(0.086, -0.507), resolution=(1080, 1920))):
                        touch(Template(r"tpl1657523566700.png", threshold=0.95, record_pos=(0.086, -0.507), resolution=(1080, 1920)))
                        wait(Template(r"tpl1657523678571.png", threshold=0.95, record_pos=(0.004, -0.844), resolution=(1080, 1920)))
                        if exists(Template(r"tpl1657523739036.png", record_pos=(-0.036, 0.15), resolution=(1080, 1920))):
                            touch(Template(r"tpl1657523739036.png", record_pos=(-0.036, 0.15), resolution=(1080, 1920)))
                            sleep()
                            touch(back_fede_shafaf)
                        else:
                            touch(back_fede_shafaf)
                            
                        
                else:
                    touch(back_fede_shafaf)
                    sleep(1)
                    touch((557, 1450))
    except:
        print('axcept mahrat amer')
        youm_alhath()


# script content



# generate html report
# from airtest.report.report import simple_report
# simple_report(__file__, logpath=None)