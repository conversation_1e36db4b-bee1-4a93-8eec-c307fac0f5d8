# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup

import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
#from airtest.core.api import *
#auto_setup(__file__)
#ST.PROJECT_ROOT = "C:\\bootGames\\image\\close.air"
ST.PROJECT_ROOT = "C:\\bootGames"
using(r"image/startGames")
from startGames import *
# script content



# generate html report
# from airtest.report.report import simple_report
# simple_report(__file__, logpath=None)

back_shahen = Template(r"tpl1656511974558.png", threshold=0.8, record_pos=(-0.431, -0.849), resolution=(1080, 1920))
back_fede_shafaf = (83, 51)
out_kal3ah = Template(r"tpl1674761764388.png", record_pos=(0.383, 0.829), resolution=(1080, 1920))
in_kal3ah = Template(r"tpl1674761730237.png", threshold=0.9, record_pos=(0.383, 0.819), resolution=(1080, 1920))

open_menu = Template(r"tpl1656516125155.png", threshold=0.95, record_pos=(0.477, -0.079), resolution=(1080, 1920))
close_menu1 = Template(r"tpl1656516305443.png", threshold=0.95, record_pos=(-0.338, 0.009), resolution=(1080, 1920))
close_1 = Template(r"tpl1745227869999.png", record_pos=(0.439, -0.753), resolution=(1080, 1920))
maharet_amer = Template(r"tpl1656605581306.png", threshold=0.95, record_pos=(-0.241, 0.13), resolution=(1080, 1920))


out_first_fild = Template(r"tpl1663827072781.png", threshold=0.95, record_pos=(0.409, -0.546), resolution=(1080, 1920))

nathra = Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920))

retarn_login = Template(r"tpl1665991888622.png", record_pos=(0.004, 0.142), resolution=(1080, 1920))

erorr_internit = Template(r"tpl1664611206710.png", threshold=0.85, record_pos=(0.193, -0.184), resolution=(1080, 1920))
othran_erorr_intenet = Template(r"tpl1665868643224.png", record_pos=(0.123, -0.192), resolution=(1080, 1920))
entabehHonakAmer = Template(r"tpl1665173073256.png", record_pos=(0.107, -0.189), resolution=(1080, 1920))
cancel1 = Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920))
Go_masera = Template(r"tpl1666674294447.png", record_pos=(-0.237, 0.814), resolution=(1080, 1920))
close_2 = Template(r"tpl1666765479913.png", record_pos=(0.406, -0.669), resolution=(1080, 1920))
vipfild =  Template(r"tpl1667168966926.png", record_pos=(-0.194, -0.265), resolution=(1080, 1920))
da3em_alkwat = Template(r"tpl1668150851166.png", record_pos=(0.006, -0.57), resolution=(1080, 1920))
fild_wahesh = Template(r"tpl1665987076389.png", record_pos=(0.005, 0.556), resolution=(1080, 1920))
baheth_wahesh = Template(r"tpl1666629471938.png", record_pos=(0.001, 0.616), resolution=(1080, 1920))
######################################################################
def xoutfirstFild():
    if exists(out_first_fild):
        touch(out_first_fild)

def close_first():
    try:
        if exists(Template(r"tpl1656511365885.png", threshold=0.95, record_pos=(0.342, -0.61), resolution=(1080, 1920))):
            touch(Template(r"tpl1656511365885.png", threshold=0.95, record_pos=(0.342, -0.61), resolution=(1080, 1920)))
            sleep(2)
        if exists(back_shahen):
            touch(back_shahen)
    except:
        print('close first except')
        pass
def Fretarn_login():
    try:
        if exists(retarn_login):
            touch([543, 1103])
    except:
        print('retarn log in')
def Ferorr_internit():
    try:
        if exists(erorr_internit):
            touch(Template(r"tpl1664611257485.png", threshold=0.85, record_pos=(0.003, 0.14), resolution=(1080, 1920)))
            sleep()
    except:
        print('erorr_internit')
def Fothran_erorr_intenet():
    try:
        if exists(othran_erorr_intenet):
            touch(Template(r"tpl1664611393479.png", threshold=0.85, record_pos=(0.0, 0.138), resolution=(1080, 1920)))
    except:
        print('othran_erorr_intenet')
def whesh_fild():
    try:
        if exists(Template(r"tpl1665987076389.png", record_pos=(0.005, 0.556), resolution=(1080, 1920))):
            touch([475, 426])
            
    except:
        print("except fild")
def baheth_wahsh():
    if exists(Template(r"tpl1666629471938.png", record_pos=(0.001, 0.616), resolution=(1080, 1920))):
        touch([482, 431])
    

def youm_alhath():
    try:
        if exists(Template(r"tpl1664611257485.png", threshold=0.85, record_pos=(0.003, 0.14), resolution=(1080, 1920))):
            touch(Template(r"tpl1664611257485.png", threshold=0.85, record_pos=(0.003, 0.14), resolution=(1080, 1920)))
            touch(back_shahen)
        if exists(vipfild):
            touch((557, 1410))
        if exists(Template(r"tpl1666007846371.png", threshold=0.95, record_pos=(0.166, -0.188), resolution=(1080, 1920))):
            touch([541, 1103])
        if exists(da3em_alkwat):
            touch(Template(r"tpl1668151679070.png", record_pos=(-0.169, 0.265), resolution=(1080, 1920)))
        if exists(Template(r"tpl1665865336924.png", threshold=0.85, record_pos=(0.412, 0.656), resolution=(1080, 1920))):
            touch([559, 102])
            wait(Template(r"tpl1665865556251.png", threshold=0.85, record_pos=(0.004, -0.37), resolution=(1080, 1920)))
            touch([461, 412])            
        if exists(Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920))):
            touch(Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920)))
        if exists(Template(r"tpl1665987076389.png", threshold=0.95, record_pos=(0.005, 0.556), resolution=(1080, 1920))):
            touch([475, 426])
        if exists(Template(r"tpl1665173073256.png", record_pos=(0.107, -0.189), resolution=(1080, 1920))):
            touch(Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920)))
        if exists(Template(r"tpl1666629471938.png", threshold=0.95, record_pos=(0.001, 0.616), resolution=(1080, 1920))):
            touch([482, 431])
        if exists(Template(r"tpl1666674294447.png", record_pos=(-0.237, 0.814), resolution=(1080, 1920))):
            touch(Template(r"tpl1666674294447.png", record_pos=(-0.237, 0.814), resolution=(1080, 1920)))        
        if exists(back_shahen):
            touch(back_shahen)
        if exists(Template(r"tpl1657097432163.png", threshold=0.95, record_pos=(0.003, -0.863), resolution=(1080, 1920))):
            touch(back_fede_shafaf)
        if exists(close_menu1):
            touch(close_menu1)
        if exists(Template(r"tpl1666536682597.png", record_pos=(0.001, -0.806), resolution=(1080, 1920))):
            touch(Template(r"tpl1666536981731.png", record_pos=(0.006, 0.793), resolution=(1080, 1920)))
            sleep()
            touch(back_shahen)
        if exists(Template(r"tpl1666536739491.png", record_pos=(-0.001, 0.075), resolution=(1080, 1920))):
            touch([548, 1809])
            sleep()
            touch(Template(r"tpl1666536981731.png", record_pos=(0.006, 0.793), resolution=(1080, 1920)))
            sleep()
            touch(back_shahen)
        if exists(Template(r"tpl1656518488432.png", threshold=0.95, record_pos=(0.002, 0.134), resolution=(1080, 1920))):
            touch(Template(r"tpl1656518488432.png", threshold=0.95, record_pos=(0.002, 0.134), resolution=(1080, 1920)))

    except:
        print('close all test')
def open_menu_to_o5ra():
    try:       
        if exists(Template(r"tpl1656516125155.png", threshold=0.95, record_pos=(0.477, -0.079), resolution=(1080, 1920))):
            touch(Template(r"tpl1656516125155.png", threshold=0.95, record_pos=(0.477, -0.079), resolution=(1080, 1920)))
            wait(Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920)))
            if exists(Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920))):
                touch((419, 471))
                swipe([590, 1424], [590, 629])               
                wait(Template(r"tpl1656518004304.png", threshold=0.95, record_pos=(0.427, 0.279), resolution=(1080, 1920)))
        else:
            close_menu()
                
    except:
        print('axcept aopen menu to o5ra')
        close_menu()
        aout_2_in()
def close_menu():
    try:
        if exists(Template(r"tpl1656516305443.png", threshold=0.95, record_pos=(-0.338, 0.009), resolution=(1080, 1920))):
            touch(Template(r"tpl1656516305443.png", threshold=0.95, record_pos=(-0.338, 0.009), resolution=(1080, 1920)))
    except:
        print('axcept close menu')
        
def aout_2_in():
        try:
            if exists(out_kal3ah):
                touch(out_kal3ah)
                wait(Template(r"tpl1666002930906.png", record_pos=(0.005, 0.594), resolution=(1080, 1920)))
                wait(in_kal3ah)
        except:
            print('axcept awout to in')
            youm_alhath()
def in_2_aout():
    try:
        if exists(in_kal3ah):
            touch(in_kal3ah)
            wait(Template(r"tpl1666002930906.png", record_pos=(0.005, 0.594), resolution=(1080, 1920)))
            wait(out_kal3ah)
    except:
        print('in to aout')
        youm_alhath()
        
def open_menu_forsan():    
    try:
        if exists(open_menu):
            touch(open_menu)
            try:
                wait(Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920)), timeout=7)
                swipe([620, 590], [620, 1399])
                
            except:
                pass

            if exists(Template(r"tpl1656656837990.png", threshold=0.95, target_pos=4, record_pos=(0.065, 0.083), resolution=(1080, 1920))):
                touch(Template(r"tpl1656656837990.png", threshold=0.95, target_pos=4, record_pos=(0.065, 0.083), resolution=(1080, 1920)))
                sleep()
            else:
                close_menu()
    except:
        print('except aopen menu forsan')
        close_menu()
def open_menu_To_al3asefah():    
    try:
        if exists(open_menu):
            touch(open_menu)
            try:
                wait(Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920)), timeout=7)
                swipe([620, 590], [620, 1399])
                
            except:
                pass

            if exists(Template(r"tpl1684649862946.png", target_pos=4, record_pos=(0.097, 0.398), resolution=(1080, 1920))):
                touch(Template(r"tpl1684649862946.png", target_pos=4, record_pos=(0.097, 0.398), resolution=(1080, 1920)))
                sleep()
            else:
                close_menu()
    except:
        print('except aopen menu forsan')
        close_menu()
def open_menu_romah():    
    try:
        if exists(open_menu):
            touch(open_menu)
            try:
                wait(Template(r"tpl1656516175469.png", threshold=0.95, record_pos=(0.097, -0.54), resolution=(1080, 1920)), timeout=7)
                swipe([620, 590], [620, 1399])
                
            except:
                pass

            if exists(Template(r"tpl1676029035911.png", threshold=0.95, target_pos=4, record_pos=(0.093, 0.162), resolution=(1080, 1920))):
                touch(Template(r"tpl1676029035911.png", threshold=0.95, target_pos=4, record_pos=(0.093, 0.162), resolution=(1080, 1920)))
                sleep()
            else:
                close_menu()
    except:
        print('except aopen menu forsan')
        close_menu()

