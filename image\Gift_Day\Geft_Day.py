# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
GoBlue = Template(r"tpl1680030212261.png", record_pos=(0.004, 0.215), resolution=(1080, 1920))

MhamDay = Template(r"tpl1680029471542.png", threshold=0.95, record_pos=(0.058, 0.86), resolution=(1080, 1920))
MahamTop = Template(r"tpl1680029660110.png", threshold=0.95, record_pos=(0.009, -0.846), resolution=(1080, 1920))

JwezAlmena = Template(r"tpl1680030074244.png", threshold=0.95, record_pos=(0.262, -0.042), resolution=(1080, 1920))

naforah_1 = Template(r"tpl1744926116527.png", threshold=0.98, record_pos=(0.238, 0.657), resolution=(1080, 1920))

TahlofTop = Template(r"tpl1680032824082.png", record_pos=(0.005, -0.847), resolution=(1080, 1920))

#################################################################################
def Getft_day():
    try:
        
        if exists(back_shahen):
            touch(back_shahen)

        if exists(out_kal3ah):
            aout_2_in()
        for x in range(9):
            if x == 9:
                break



            if exists(MhamDay):
                touch(MhamDay)
                wait(MahamTop,timeout=8)
            if not exists(Template(r"tpl1744927355703.png", threshold=0.95, record_pos=(0.311, -0.123), resolution=(1080, 1920))):
                touch([0.813, 0.423])
                touch([0.509, 0.759])
                touch([0.5, 0.105])
            if not exists(Template(r"tpl1744927453151.png", threshold=0.95, record_pos=(0.204, -0.118), resolution=(1080, 1920))):
                touch([0.813, 0.423])
                touch([0.509, 0.759])
                touch([0.5, 0.105])
            if exists(JwezAlmena):

                touch(JwezAlmena)
                wait(Template(r"tpl1744925699551.png", record_pos=(0.309, -0.113), resolution=(1080, 1920)))

                touch(GoBlue)
                wait(Template(r"tpl1744929773772.png", threshold=0.9, record_pos=(0.187, -0.769), resolution=(1080, 1920)))
                touch([0.881, 0.29])
                wait(back_shahen,timeout=8)
                touch(back_shahen)
                wait(MhamDay,timeout=8)
                if exists(MhamDay):
                    touch(MhamDay)
                    wait(MahamTop,timeout=8) 
            if exists(naforah_1):
                touch(naforah_1)
                wait(Template(r"tpl1744926221933.png", record_pos=(0.091, -0.19), resolution=(1080, 1920)),timeout=8)
                touch(GoBlue)
                wait(Template(r"tpl1744926298999.png", record_pos=(0.02, -0.198), resolution=(1080, 1920)),timeout=8)
                touch([0.509, 0.423])
                wait(Template(r"tpl1744926345079.png", record_pos=(-0.101, 0.034), resolution=(1080, 1920)),timeout=8)
                touch([0.392, 0.521])
                wait(Template(r"tpl1744926379518.png", record_pos=(0.006, -0.846), resolution=(1080, 1920)),timeout=8)
                if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                    touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])            
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])                        
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])            
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])                        
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])            
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])                        
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926469064.png", record_pos=(0.03, 0.079), resolution=(1080, 1920))):
                        touch([0.198, 0.706])
                    if exists(Template(r"tpl1744926782529.png", record_pos=(0.105, 0.141), resolution=(1080, 1920))):
                        touch([0.453, 0.234])
                        wait(back_shahen,timeout=8)
                        touch(back_shahen)
                else:
                    touch(back_shahen)
            if exists(Template(r"tpl1744930949118.png", threshold=0.95, record_pos=(0.254, 0.671), resolution=(1080, 1920))):
                touch(Template(r"tpl1744930949118.png", threshold=0.95, record_pos=(0.254, 0.671), resolution=(1080, 1920)))
                wait(Template(r"tpl1744930996632.png", threshold=0.9, record_pos=(0.315, -0.114), resolution=(1080, 1920)),timeout=8)
                touch(GoBlue)
                wait(Template(r"tpl1744931063055.png", threshold=0.95, record_pos=(0.006, -0.041), resolution=(1080, 1920)))
                touch(Template(r"tpl1744931063055.png", threshold=0.95, record_pos=(0.006, -0.041), resolution=(1080, 1920)))
                wait(Template(r"tpl1744931128665.png", threshold=0.95, record_pos=(0.172, 0.208), resolution=(1080, 1920)),timeout=8)
                touch(Template(r"tpl1744931128665.png", threshold=0.95, record_pos=(0.172, 0.208), resolution=(1080, 1920)))
                wait(Template(r"tpl1744931184045.png", record_pos=(0.0, -0.847), resolution=(1080, 1920)),timeout=8)
                touch([0.836, 0.915])
                wait(Template(r"tpl1744931229771.png", record_pos=(0.004, -0.846), resolution=(1080, 1920)))               
                if exists(Template(r"tpl1744931269469.png", threshold=0.95, record_pos=(0.203, 0.463), resolution=(1080, 1920))):
                    touch(Template(r"tpl1744931269469.png", threshold=0.95, record_pos=(0.203, 0.463), resolution=(1080, 1920)))
                    wait(Template(r"tpl1744931327060.png", threshold=0.95, record_pos=(-0.006, -0.6), resolution=(1080, 1920)))
                    touch([0.509, 0.06])

                    wait(Template(r"tpl1744931229771.png", record_pos=(0.004, -0.846), resolution=(1080, 1920)))
                    touch([0.502, 0.902])
                    wait(Template(r"tpl1744931685769.png", record_pos=(-0.004, -0.849), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1744931717810.png", threshold=0.95, record_pos=(0.194, 0.463), resolution=(1080, 1920))):
                        touch(Template(r"tpl1744931717810.png", threshold=0.95, record_pos=(0.194, 0.463), resolution=(1080, 1920)))
                        wait(Template(r"tpl1744931793517.png", threshold=0.95, record_pos=(-0.001, -0.601), resolution=(1080, 1920)))
                        touch([0.485, 0.049])

                else:
                    touch(back_shahen)
            else:
                swipe([543, 1648], [543, 1382])
                
    except:
        
        touch(back_shahen)
            


        
