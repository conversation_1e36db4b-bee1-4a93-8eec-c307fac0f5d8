# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
import sqlite3


#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")


ST.PROJECT_ROOT = "C:\\bootGames"
###############################################################################3

try:
    sqliteConnection = sqlite3.connect('farm.db')
    cursor = sqliteConnection.cursor()
    print("Connected to SQLite")

    sqlite_select_query = """SELECT * from information"""
    cursor.execute(sqlite_select_query)
    records = cursor.fetchall()
    print("Total rows are:  ", len(records))
    #print("Printing each row")
    '''for row in records:
        #print("ID: ", row[0])
        #print("User Name:",row[1])
        #print("Password:",row[2])
        #print("Taib Fild:",row[3])
        #print("N maRsh:",row[4])
        #print("lvl Fild:", row[5])
        #print("Taib ston:", row[6])
        print("\n")'''

    #print(records[0][0])



    #cursor.close()

except sqlite3.Error as error:
    print("Failed to read data from sqlite table", error)
finally:
    if sqliteConnection:
        sqliteConnection.close()
        print("The SQLite connection is closed")

