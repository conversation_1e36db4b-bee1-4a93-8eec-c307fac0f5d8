# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")


ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *

##############################################################################

 

def speedFarm():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        if exists(Template(r"tpl1685679131957.png", record_pos=(-0.427, 0.468), resolution=(1080, 1920))):
            touch([79, 1464])
            wait(Template(r"tpl1685679164244.png", record_pos=(0.003, -0.851), resolution=(1080, 1920)))
            touch([298, 165])
            sleep()
            if exists(Template(r"tpl1685679250502.png", target_pos=8, record_pos=(0.144, -0.345), resolution=(1080, 1920))):
                touch(Template(r"tpl1685679250502.png", target_pos=8, record_pos=(0.144, -0.345), resolution=(1080, 1920)))
                wait(Template(r"tpl1685679318381.png", record_pos=(0.003, -0.843), resolution=(1080, 1920)))
                if exists(Template(r"tpl1685679341709.png", record_pos=(0.366, -0.244), resolution=(1080, 1920))):
                    touch(Template(r"tpl1685679403192.png", record_pos=(-0.323, -0.153), resolution=(1080, 1920)))
                    sleep()
                    touch([69, 46])
                    sleep()
                    touch([69, 46])                    
                else:
                    touch([69, 46])
                    sleep()
                    touch([69, 46])
            


    except:
        print('except speed')
        youm_alhath()
