# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
def fild_in_in():
    try:

        if exists(back_shahen):
            touch(back_shahen)

        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        open_menu_romah()
        
        swipe([977, 923],[219, 923])
        sleep(1)
        if exists(Template(r"tpl1656606805528.png", threshold=0.95, record_pos=(0.143, -0.4), resolution=(1080, 1920))):
            touch(Template(r"tpl1656606805528.png", threshold=0.95, record_pos=(0.143, -0.4), resolution=(1080, 1920)))
            sleep(2)
        if exists(Template(r"tpl1656606865225.png", threshold=0.95, record_pos=(0.214, -0.289), resolution=(1080, 1920))):
            touch(Template(r"tpl1656606865225.png", threshold=0.95, record_pos=(0.214, -0.289), resolution=(1080, 1920)))
            sleep(2)
        if exists(Template(r"tpl1656606929418.png", threshold=0.95, record_pos=(-0.03, -0.278), resolution=(1080, 1920))):
            touch(Template(r"tpl1656606929418.png", threshold=0.95, record_pos=(-0.03, -0.278), resolution=(1080, 1920)))
            sleep(2)
        if exists(Template(r"tpl1656607002101.png", threshold=0.95, record_pos=(0.093, -0.173), resolution=(1080, 1920))):
            touch(Template(r"tpl1656607002101.png", threshold=0.95, record_pos=(0.093, -0.173), resolution=(1080, 1920)))
            sleep(2)     
        
    except:
        print('fild in calcolate')
        youm_alhath()
