<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>add_D</class>
 <widget class="QDialog" name="add_D">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>334</width>
    <height>524</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>334</width>
    <height>524</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>334</width>
    <height>524</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QPushButton" name="add_Clear_pushButton_2">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>430</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>clear</string>
   </property>
  </widget>
  <widget class="QPushButton" name="add_add_pushButton">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>430</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="toolTip">
    <string>add Info For Farmer</string>
   </property>
   <property name="text">
    <string>Add</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>20</y>
     <width>281</width>
     <height>51</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt; font-weight:600;&quot;&gt;Add Farm&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QPushButton" name="BackAdd_pushButton">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>430</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Back </string>
   </property>
   <property name="text">
    <string>Back</string>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>140</y>
     <width>110</width>
     <height>221</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;User Name&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;password&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_3">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Taib Fild&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_4">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Number Marsh&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_5">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;field level&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_6">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Fusing gear type&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>140</y>
     <width>201</width>
     <height>231</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <widget class="QLineEdit" name="User_Name_lineEdit">
      <property name="toolTip">
       <string>User Name of Your Farm</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="Password_lineEdit">
      <property name="toolTip">
       <string>Password Your Farm</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="Taib_Fild_comboBox">
      <property name="toolTip">
       <string>Taip Fild</string>
      </property>
      <property name="currentIndex">
       <number>-1</number>
      </property>
      <item>
       <property name="text">
        <string>Wheat</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Wood</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Iron</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Silvar</string>
       </property>
      </item>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="Number_Marsh_comboBox">
      <property name="toolTip">
       <string>The number of legion for farms</string>
      </property>
      <property name="currentIndex">
       <number>-1</number>
      </property>
      <item>
       <property name="text">
        <string>1</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>2</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>3</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>4</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>5</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>6</string>
       </property>
      </item>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="Lvl_Fild_comboBox">
      <property name="toolTip">
       <string>field level</string>
      </property>
      <property name="currentIndex">
       <number>-1</number>
      </property>
      <item>
       <property name="text">
        <string>1</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>2</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>3</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>4</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>5</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>6</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>7</string>
       </property>
      </item>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="Taib_ston_comboBox">
      <property name="toolTip">
       <string>Fusing gear type</string>
      </property>
      <property name="currentIndex">
       <number>-1</number>
      </property>
      <item>
       <property name="text">
        <string>worm stone</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>upgrade stone</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>strengthening stone</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>purification stone</string>
       </property>
      </item>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
