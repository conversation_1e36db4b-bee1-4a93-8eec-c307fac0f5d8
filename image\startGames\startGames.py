# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *

ver_g = Template(r"tpl1745085864622.png", record_pos=(0.288, -0.835), resolution=(1080, 1920))
startGG = Template(r"tpl1745085724278.png", record_pos=(-0.001, 0.687), resolution=(1080, 1920))
first_ver = Template(r"tpl1745085892897.png", record_pos=(-0.118, 0.605), resolution=(1080, 1920))
close_first_pop = Template(r"tpl1745086033198.png", record_pos=(0.346, -0.609), resolution=(1080, 1920))
back_first_time = Template(r"tpl1745086062159.png", record_pos=(-0.43, -0.843), resolution=(1080, 1920))
start100_G = Template(r"tpl1745086000247.png", record_pos=(-0.123, 0.604), resolution=(1080, 1920))

def start_gg():
    wait(first_ver,timeout=100)
    wait(ver_g, timeout=100)
    wait(startGG, timeout=100)
    if exists(startGG):
        touch(startGG)
        wait(start100_G,timeout=100)
        wait(close_first_pop,timeout=100)
        touch(close_first_pop)
        wait(back_first_time,timeout=100)
        touch(back_first_time)
        sleep()

        print('انهاء بداية العبة')
        
