# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
auto_setup(__file__)
#from airtest.core.android.touch_methods.base_touch import *
import os
import sys

import random
import threading
import time



#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"

#init_device("Android", ime_method="ADBIME")
#dev = device()
#dev.yosemite_ime.code("2")
#print(dev.yosemite_ime._get_ime_list())

#ym = "com.netease.nie.yosemite/.ime.ImeService"
#sh = "com.netease.nemu_vinput.nemu/com.android.inputmethodcommon.SoftKeyboard"


#def set_ime(ime):
#    shell("ime enable " + ime)
#    shell("ime set " + ime)

#set_ime(ym)

#text("123", enter=False)

#set_ime(sh)
shell ("input text '<EMAIL>'")