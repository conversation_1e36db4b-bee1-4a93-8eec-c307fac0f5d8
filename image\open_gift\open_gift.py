# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:21513",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
#############################################################################################################3
using(r"image/close")
from close import *
##########################################################3

def open_gift():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        open_menu_to_o5ra()
        if exists(Template(r"tpl1745137417410.png", threshold=0.75, target_pos=4, record_pos=(0.124, 0.369), resolution=(1080, 1920))):
            touch(Template(r"tpl1745137417410.png", threshold=0.75, target_pos=4, record_pos=(0.124, 0.369), resolution=(1080, 1920)))
            sleep(1)
            touch([0.404, 0.138])
            sleep(3)
            if exists(Template(r"tpl1745159507192.png", record_pos=(0.383, -0.307), resolution=(1080, 1920))):
                touch([0.882, 0.286])
                wait(Template(r"tpl1745159570712.png", record_pos=(-0.413, -0.799), resolution=(1080, 1920)))
                touch(Template(r"tpl1745159570712.png", record_pos=(-0.413, -0.799), resolution=(1080, 1920)))
            else:
                touch(Template(r"tpl1745159570712.png", record_pos=(-0.413, -0.799), resolution=(1080, 1920)))
                
        else:
            close_menu()        
    except:
        print('axcept aopen gift')
        youm_alhath()
    