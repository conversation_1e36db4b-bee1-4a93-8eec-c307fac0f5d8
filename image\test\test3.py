# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup


auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
#auto_setup(__file__, logdir=None, devices=["Android:///?ime_method=ADBIME",])
#auto_setup(__file__, logdir=None, devices=["Android:///?ime_method=ADBIME",])
#init_device("Android", ime_method"ADBIME")


# script content
print("start...")



# generate html report
# from airtest.report.report import simple_report
# simple_report(__file__, logpath=None)
text("<EMAIL>")