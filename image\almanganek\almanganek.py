# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
almanganeK = Template(r"tpl1668271242420.png", threshold=0.95, record_pos=(0.2, 0.736), resolution=(1080, 1920))
top_almanganek = Template(r"tpl1668271306344.png", threshold=0.95, record_pos=(0.004, -0.846), resolution=(1080, 1920))
free_go = Template(r"tpl1668271387271.png", threshold=0.95, record_pos=(-0.04, 0.553), resolution=(1080, 1920))
top_markaz_alahdath = Template(r"tpl1668270875102.png", record_pos=(0.006, -0.85), resolution=(1080, 1920))
markaz_alahdath = Template(r"tpl1674762267667.png", threshold=0.8, rgb=True, record_pos=(-0.428, -0.645), resolution=(1080, 1920))

def almanganek():
    try:
        if exists(back_shahen):
            touch(back_shahen)

        if exists(out_kal3ah):
            aout_2_in()
        if exists(markaz_alahdath):
            touch(markaz_alahdath)
            wait(Template(r"tpl1668270875102.png", record_pos=(0.006, -0.85), resolution=(1080, 1920)), timeout=7)
            for x in range(4):
                if x == 4:
                    break
                if exists(almanganeK):
                    touch(almanganeK)
                    wait(top_almanganek)
                    if exists(free_go):
                        touch(free_go)
                        wait(top_almanganek)
                        touch(back_shahen)
                        wait(Template(r"tpl1668270875102.png", record_pos=(0.006, -0.85), resolution=(1080, 1920)), timeout=7)
                        touch(back_shahen)
                        break
                        print("ggogogogo")
                    else:
                        touch(back_shahen)
                        wait(top_markaz_alahdath)
                        touch(back_shahen)
                        break

                else:
                    if not exists(almanganeK):
                        swipe([370, 1630],[370, 1009])
                        sleep()
                        if exists(almanganeK):
                            touch(almanganeK)
                            wait(top_almanganek)
                            if exists(free_go):
                                touch(free_go)
                                wait(top_almanganek)
                                touch(back_shahen)
                                wait(Template(r"tpl1668270875102.png", record_pos=(0.006, -0.85), resolution=(1080, 1920)), timeout=7)
                                touch(back_shahen)                                
                                break
                                print("ggogogogo")
                            else:
                                touch(back_shahen)
                                wait(top_markaz_alahdath)
                                touch(back_shahen)
                                break
    except:
        print('axcept almanganeq')
        youm_alhath()        
