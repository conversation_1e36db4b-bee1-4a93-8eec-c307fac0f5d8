# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup

from datetime import datetime
import datetime as dt
# import random
import schedule
#import time
import sys
import os
from os import path
# from main import *
# import signal
import threading

##pyqt5
import sys
from PyQt5.uic import loadUi, loadUiType
from PyQt5 import uic
from PyQt5 import QtWidgets
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

import pyrebase
import sqlite3
from configparser import ConfigParser

ST.PROJECT_ROOT = "C:\\bootGames"

########## مسار المهمات#################################
using(r"image/close")
using(r"image/open_gift")
using(r"image/afreto")
using(r"image/Gift_Day")
using(r"image/Naforah")
#using(r"image/Ahgar")
using(r"image/mohmat_aldobat")
using(r"image/Tagned_Abtal")
using(r"image/Login_user")
using(r"image/fild")
using(r"image/startGames")
using(r"config/farm")
using(r"image/malekalsehr")
using(r"image/opin_box")
using(r"image/speedFarm")
using(r"image/gifEstetla")
using(r"image/defind")
using(r"image/jwaher")
using(r"image/almanganek")
using(r"image/fild_in")

###################استدعاء  المهمات#############################################
from close import *
from open_gift import *
from Habet_afret import *
from Geft_Day import *
from Naforah import *
#from ahgar import *
#from mohmat_aldobat import *
from tagned_abtal import *
from farm1 import *
from fild import *
from startGames import *
from alseher import *
from open_box import *
from speedFarm import *
from giftestetla import *
from drfind import *
from userfarm import *
from jwaher import *
from almanganek import *
from fild_in import *

###################################################################################################################


conn = sqlite3.connect('farm.db')
curs = conn.cursor()
curs.execute('CREATE TABLE IF NOT EXISTS information (id INTEGER PRIMARY KEY AUTOINCREMENT, User_Name TEXT, U_PASSWORD TEXT, Taib_Fild TEXT, Number_Marsh TEXT, lvl_Fild TEXT , Taib_of_Ston TEXT)')
conn.commit()

firebaseConfig = {
    "apiKey": "AIzaSyCrjKmHvUWgDhkpPDl3RF3dwxleQLyeQTk",
    "authDomain": "bootgames.firebaseapp.com",
    "projectId": "bootgames",
    "storageBucket": "bootgames.appspot.com",
    "messagingSenderId": "837493303047",
    "appId": "1:837493303047:web:03e1416ef16c51f05d87a2",
    "measurementId": "G-2PMYDHFD5L",
    "databaseURL": "https://bootgames-default-rtdb.europe-west1.firebasedatabase.app/"}

firebase = pyrebase.initialize_app(firebaseConfig)
auth = firebase.auth()
db = firebase.database()
################time########################


#######################################

SETTINGS_Geft_Day1 = 'settings/Geft_Day1'
SETTINGS_open_gift1 = 'settings/open_gift1'
SETTINGS_Hebet_afret1 = 'settings/GHebet_afret1'
SETTINGS_Tagned_Abtal1 = 'settings/Tagned_Abtal1'
#SETTINGS_ahgar1 = 'settings/ahgar1'
SETTINGS_mohmat_aldobat1 = 'settings/mohmat_aldobat1'
SETTINGS_Naforah1 = 'settings/Naforah1'
SETTINGS_fild_B = 'settings/fild_B'
SETTINGS_open_Box_M = 'settings/open_Box_M'
SETTINGS_speed_farm = 'settings/speed_farm'
SETTINGS_geft_estetla = 'settings/goft_estetla'
SETTINGS_defind3D = 'settings/defind3D'
SETTINGS_almanganek = 'settings/almanganek'
SETTINGS_fild_in = 'settings/fild_in'

checkBox = None
checkBox_2 = None
checkBox_3 = None
checkBox_4 = None
checkBox_5 = None
checkBox_6 = None
checkBox_7 = None
checkBox_8 = None
checkBox_10 = None
checkBox_11 = None
checkBox_12 = None
checkBox_13 = None
checkBox_15 = None
checkBox_16 = None
##########----------------------


#####################################

class loginM(QDialog):
    def __init__(self):
        super(loginM, self).__init__()
        loadUi("login.ui", self)

        # self.bo_login.clicked.connect(self.gotologin)      #اذا تم تفعيلة يذهب مباشرة بدون التحقق
        self.lineEdit_password.setEchoMode(QtWidgets.QLineEdit.Password)
        self.bo_login.clicked.connect(self.Logifunction)  # لتفعيل التحقق باسم مستخدم و باسوورد
        self.bo_login_2.clicked.connect(self.write_user)
        #######################################################
        self.error.setText("Conect To Emuletar successful")
        os.system('adb.exe connect 127.0.0.1:7555')
        #####################قرائة من ملف اليوز ################
        parser1 = ConfigParser()
        parser1.read("username.ini")
        get_username = parser1.get('USERINFO', 'dbuser')
        get_password = parser1.get('USERINFO', 'dbpass')

        self.lineEdit_username.setText(get_username)
        self.lineEdit_password.setText(get_password)

    ########################حفظ ما تكتبة يوزر ################
    def write_user(self):
        parser1 = ConfigParser()
        try:
            self.error.setText("تم الحفظ بنجاح")
            parser1["USERINFO"] = {
                "dbuser": self.lineEdit_username.text(),
                "dbpass": self.lineEdit_password.text()
            }
            with open('username.ini', 'w') as conf:
                parser1.write(conf)
        except:
            self.error.setText("تم حفظ البيانات")

    ###########################################################################
    def Logifunction(self):

        email = self.lineEdit_username.text()
        password = self.lineEdit_password.text()

        try:
            if len(email) == 0 or len(password) == 0:
                self.error.setText("تاكد من الايميل و الرقم السري")


            else:
                # auth.sign_in_with_email_and_password(email, password)
                user = auth.sign_in_with_email_and_password(email, password)
                # before the 1 hour expiry:
                user = auth.refresh(user['refreshToken'])
                # now we have a fresh token
                user['idToken']
                self.bo_login.clicked.connect(self.gotologin)
                self.error.setText("لقد تم الاتصال بنجاح الرجاء اضغط continu...")
                self.bo_login.setText('continu')
                try:
                    if not cli_setup():
                        #self.error.setText("Conect To Emuletar successful")
                        #os.system('adb.exe connect 127.0.0.1:7555')
                        auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555", ],
                                   project_root="C:/bootGames")
                        #auto_setup(__file__, logdir=None, devices=["Android:///?ime_method=ADBIME", ])

                except:
                    # def wilcom_user(self):
                    # Pop up Box
                    msgu = QMessageBox()
                    msgu.setWindowTitle("تنبيه!!!")
                    msgu.setText(" برنامج Memu play لا يعمل")
                    msgu.setIcon(QMessageBox.Information)
                    xu = msgu.exec_()
                    self.error.setText("الرجاء تشغيل Memu Play واعادة تشغيل البرنامج")


        except:
            self.error.setText("تاكد من كتابة اسم المستخدم او الرقم السري بشكل صحيح ")

    def gotologin(self):
        login = meapp()
        widget.addWidget(login)
        widget.setCurrentIndex(widget.currentIndex() + 1)


class meapp(QDialog):
    def __init__(self):
        super(meapp, self).__init__()
        loadUi("main2.ui", self)

        self.thread_manager = QThreadPool()
        self.thread_Mohemat = QThreadPool()
        self.th_pop = QThreadPool()
        self.th_Defind_3D = QThreadPool()
        self.Button_programs()
        self.exit_click()
        self.label.setText("if first time Go Tools Add farm ")
        self.label_2.setText("ايميل المزرعة")
        self.label_3.setText("نوع الاحجار")
        #########################################
        self.stop_Tool_pushButton.clicked.connect(self.Stoppro)
        # self.getDeveloperInfo(2)
        self.label_5.setText(str(len(records)-1))
        #################################################checkBocx########
        # Get settings
        settings = QSettings("ConfigS.ini", QSettings.IniFormat)
        # Get checkbox state with speciying type of checkbox:
        # type=bool is a replacement of toBool() in PyQt5
        check_state = settings.value(SETTINGS_Geft_Day1, False, type=bool)
        check_state2 = settings.value(SETTINGS_open_gift1, False, type=bool)
        check_state3 = settings.value(SETTINGS_Hebet_afret1, False, type=bool)
        check_state4 = settings.value(SETTINGS_Tagned_Abtal1, False, type=bool)
        #check_state5 = settings.value(SETTINGS_ahgar1, False, type=bool)
        check_state6 = settings.value(SETTINGS_mohmat_aldobat1, False, type=bool)
        check_state7 = settings.value(SETTINGS_Naforah1, False, type=bool)
        check_state8 = settings.value(SETTINGS_fild_B, False, type=bool)
        check_state10 = settings.value(SETTINGS_open_Box_M, False, type=bool)
        check_state11 = settings.value(SETTINGS_speed_farm, False, type=bool)
        check_state12 = settings.value(SETTINGS_geft_estetla, False, type=bool)
        check_state13 = settings.value(SETTINGS_defind3D, False, type=bool)
        check_state15 = settings.value(SETTINGS_almanganek, False, type=bool)
        check_state16 = settings.value(SETTINGS_fild_in, False, type=bool)

        # Set state
        self.checkBox.setChecked(check_state)
        self.checkBox_2.setChecked(check_state2)
        self.checkBox_3.setChecked(check_state3)
        self.checkBox_4.setChecked(check_state4)
        #self.checkBox_5.setChecked(check_state5)
        self.checkBox_6.setChecked(check_state6)
        self.checkBox_7.setChecked(check_state7)
        self.checkBox_8.setChecked(check_state8)
        self.checkBox_10.setChecked(check_state10)
        self.checkBox_11.setChecked(check_state11)
        self.checkBox_12.setChecked(check_state12)
        self.checkBox_13.setChecked(check_state13)
        self.checkBox_15.setChecked(check_state15)
        self.checkBox_16.setChecked(check_state16)
        # connect the slot to the signal by clicking the checkbox to save the state settings
        self.checkBox.clicked.connect(self.save_check_box_settings)
        self.checkBox_2.clicked.connect(self.save_check_box_settings)
        self.checkBox_3.clicked.connect(self.save_check_box_settings)
        self.checkBox_4.clicked.connect(self.save_check_box_settings)
        #self.checkBox_5.clicked.connect(self.save_check_box_settings)
        self.checkBox_6.clicked.connect(self.save_check_box_settings)
        self.checkBox_7.clicked.connect(self.save_check_box_settings)
        self.checkBox_8.clicked.connect(self.save_check_box_settings)
        self.checkBox_10.clicked.connect(self.save_check_box_settings)
        self.checkBox_11.clicked.connect(self.save_check_box_settings)
        self.checkBox_12.clicked.connect(self.save_check_box_settings)
        self.checkBox_13.clicked.connect(self.save_check_box_settings)
        self.checkBox_15.clicked.connect(self.save_check_box_settings)
        self.checkBox_16.clicked.connect(self.save_check_box_settings)

        # Slot checkbox to save the settings

    def save_check_box_settings(self):
        settings = QSettings("ConfigS.ini", QSettings.IniFormat)
        settings.setValue(SETTINGS_Geft_Day1, self.checkBox.isChecked())
        settings.setValue(SETTINGS_open_gift1, self.checkBox_2.isChecked())
        settings.setValue(SETTINGS_Hebet_afret1, self.checkBox_3.isChecked())
        settings.setValue(SETTINGS_Tagned_Abtal1, self.checkBox_4.isChecked())
        #settings.setValue(SETTINGS_ahgar1, self.checkBox_5.isChecked())
        settings.setValue(SETTINGS_mohmat_aldobat1, self.checkBox_6.isChecked())
        settings.setValue(SETTINGS_Naforah1, self.checkBox_7.isChecked())
        settings.setValue(SETTINGS_fild_B, self.checkBox_8.isChecked())
        settings.setValue(SETTINGS_open_Box_M, self.checkBox_10.isChecked())
        settings.setValue(SETTINGS_speed_farm, self.checkBox_11.isChecked())
        settings.setValue(SETTINGS_geft_estetla, self.checkBox_12.isChecked())
        settings.setValue(SETTINGS_defind3D, self.checkBox_13.isChecked())
        settings.setValue(SETTINGS_almanganek, self.checkBox_15.isChecked())
        settings.setValue(SETTINGS_fild_in, self.checkBox_16.isChecked())
        settings.sync()
        ##################################################

        self.time1 = datetime.now()
        ####################################################
    def th_defind(self):
        self.label.setText("Stard Defind Farm 3 Day")
        defind()
        #self.th_Defind_3D.start(defind)
        if exists(in_kal3ah):
            msg = QMessageBox()
            msg.setWindowTitle("Log in farm Defind 3 Day")
            msg.setText("Defind 3 Day successfully")
            msg.setIcon(QMessageBox.Information)
            x = msg.exec_()
    def Login_next(self):

        go_to_farm = self.lineEdit.text()
        self.label.setText("log in farm")
        #if exists(in_kal3ah):
        #    # Pop up Box
        #    msg = QMessageBox()
        #    msg.setWindowTitle("Log in farm")
        #    msg.setText("click ok to start Log in")
        #    msg.setIcon(QMessageBox.Information)
        #    x = msg.exec_()

        try:            
            if go_to_farm == "1":
                login_farm1()
                self.label.setText("log in farm1")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm1")
                msg.setText("Log in the farm1 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()

            if go_to_farm == "2":
                login_farm2()
                self.label.setText("log in farm2")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm2")
                msg.setText("Log in the farm2 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "3":
                login_farm3()
                self.label.setText("log in farm3")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm3")
                msg.setText("Log in the farm3 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "4":
                login_farm4()
                self.label.setText("log in farm4")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm4")
                msg.setText("Log in the farm4 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "5":
                login_farm5()
                self.label.setText("log in farm5")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm5")
                msg.setText("Log in the farm5 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "6":
                login_farm6()
                self.label.setText("log in farm6")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm6")
                msg.setText("Log in the farm6 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "7":
                login_farm7()
                self.label.setText("log in farm7")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm7")
                msg.setText("Log in the farm7 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "8":
                login_farm8()
                self.label.setText("log in farm8")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm8")
                msg.setText("Log in the farm8 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "9":
                login_farm9()
                self.label.setText("log in farm9")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm9")
                msg.setText("Log in the farm9 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "10":
                login_farm10()
                self.label.setText("log in farm10")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm10")
                msg.setText("Log in the farm10 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "11":
                login_farm11()
                self.label.setText("log in farm11")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm11")
                msg.setText("Log in the farm11 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "12":
                login_farm12()
                self.label.setText("log in farm12")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm12")
                msg.setText("Log in the farm12 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "13":
                login_farm13()
                self.label.setText("log in farm13")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm13")
                msg.setText("Log in the farm13 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "14":
                login_farm14()
                self.label.setText("log in farm14")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm14")
                msg.setText("Log in the farm14 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "15":
                login_farm15()
                self.label.setText("log in farm15")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm15")
                msg.setText("Log in the farm15 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "16":
                login_farm16()
                self.label.setText("log in farm16")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm16")
                msg.setText("Log in the farm16 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "17":
                login_farm17()
                self.label.setText("log in farm17")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm17")
                msg.setText("Log in the farm17 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "18":
                login_farm18()
                self.label.setText("log in farm18")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm18")
                msg.setText("Log in the farm18 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "19":
                login_farm19()
                self.label.setText("log in farm19")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm19")
                msg.setText("Log in the farm19 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "20":
                login_farm20()
                self.label.setText("log in farm20")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm20")
                msg.setText("Log in the farm20 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "21":
                login_farm21()
                self.label.setText("log in farm21")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm21")
                msg.setText("Log in the farm21 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "22":
                login_farm22()
                self.label.setText("log in farm22")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm22")
                msg.setText("Log in the farm22 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "23":
                login_farm23()
                self.label.setText("log in farm23")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm23")
                msg.setText("Log in the farm23 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "24":
                login_farm24()
                self.label.setText("log in farm24")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm24")
                msg.setText("Log in the farm24 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "25":
                login_farm25()
                self.label.setText("log in farm25")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm25")
                msg.setText("Log in the farm25 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "26":
                login_farm26()
                self.label.setText("log in farm26")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm26")
                msg.setText("Log in the farm26 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "27":
                login_farm27()
                self.label.setText("log in farm27")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm27")
                msg.setText("Log in the farm27 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "28":
                login_farm28()
                self.label.setText("log in farm28")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm28")
                msg.setText("Log in the farm28 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "29":
                login_farm29()
                self.label.setText("log in farm29")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm29")
                msg.setText("Log in the farm29 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "30":
                login_farm30()
                self.label.setText("log in farm30")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm30")
                msg.setText("Log in the farm30 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "31":
                login_farm31()
                self.label.setText("log in farm31")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm31")
                msg.setText("Log in the farm31 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "32":
                login_farm32()
                self.label.setText("log in farm32")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm32")
                msg.setText("Log in the farm32 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "33":
                login_farm33()
                self.label.setText("log in farm33")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm33")
                msg.setText("Log in the farm33 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "34":
                login_farm34()
                self.label.setText("log in farm34")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm")
                msg.setText("Log in the farm34 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "35":
                login_farm35()
                self.label.setText("log in farm35")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm35")
                msg.setText("Log in the farm35 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "36":
                login_farm36()
                self.label.setText("log in farm36")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm36")
                msg.setText("Log in the farm36 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "37":
                login_farm37()
                self.label.setText("log in farm37")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm37")
                msg.setText("Log in the farm37 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "38":
                login_farm38()
                self.label.setText("log in farm38")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm38")
                msg.setText("Log in the farm38 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "39":
                login_farm39()
                self.label.setText("log in farm39")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm39")
                msg.setText("Log in the farm39 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "40":
                login_farm40()
                self.label.setText("log in farm40")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm40")
                msg.setText("Log in the farm40 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "41":
                login_farm41()
                self.label.setText("log in farm41")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm41")
                msg.setText("Log in the farm41 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "42":
                login_farm42()
                self.label.setText("log in farm42")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm42")
                msg.setText("Log in the farm42 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "43":
                login_farm43()
                self.label.setText("log in farm43")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm43")
                msg.setText("Log in the farm43 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "44":
                login_farm44()
                self.label.setText("log in farm44")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm44")
                msg.setText("Log in the farm44 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
            if go_to_farm == "45":
                login_farm45()
                self.label.setText("log in farm45")
                # Pop up Box
                msg = QMessageBox()
                msg.setWindowTitle("Log in farm45")
                msg.setText("Log in the farm45 successfully")
                msg.setIcon(QMessageBox.Information)
                x = msg.exec_()
        except:
            self.label.setText("The Inbut is Rong???")

    def one_pop(self):
        while True:
            try:
                print('looooooooooooooooooooooooop pop')
                if exists(close_1):
                    print('pop close first')
                    close_first()
                elif exists(retarn_login):
                    print('pop retarn_login')
                    Fretarn_login()
                elif exists(erorr_internit):
                    print('pop erorr internit')
                    Ferorr_internit()
                elif exists(othran_erorr_intenet):
                    print('pop other eror enternit')
                    Fothran_erorr_intenet()
                elif exists(out_first_fild):
                    xoutfirstFild()
                elif exists(close_2):
                    touch(close_2)

                continue
            except:
                pass

    def f_pop(self):
        print('one-pop')
        self.th_pop.start(self.one_pop)

    def thred_1(self):
        print('thred111111111')
        stop_app("com.and.riseofthekings")
        self.thread_manager.start(self.RunGames)
        self.thread_Mohemat.start(self.Mohemat)  # ...since .start() is used!
        self.label.setText("الدخول الى اللعبة ....")
        # self.start_bot.hide()      #....اخفاء الزر
        self.start_bot.setEnabled(False)  # ....جعل الزر غير فعال
        self.add_farm_m2_pushButton.setEnabled(False)
        self.pushButton.setEnabled(False)
        self.pushButton_2.setEnabled(False)
        self.StartTools_pushButton_2.setEnabled(False)


    def thred_2(self):
        print('thred2222222222')
        #########killbroses all#############
        os.system('adb_kill-server.cmd')
        ######################################
        self.thread_Mohemat.stop(self.Mohemat)  # ...since .stop() is used!
        self.thread_manager.stop(self.Tolls_mohemat)
    def thred_3(self):
        print('thred333333333')
        self.thread_manager.start(self.Tolls_mohemat)
        self.start_bot.setEnabled(False)  # ....جعل الزر غير فعال

    def RunGames(self):
        start_app("com.netease.nie.yosemite")
        sleep(1)
        start_app("com.and.riseofthekings")
        print('RunGamessssssssssssssssssssssssssssssss')
        sleep(2)
        wait(ver_g,timeout=100)
        start_gg()
        #self.f_pop()
    def Tolls_mohemat(self):
        if self.checkBox_9.isChecked() == True:
            self.label.setText("Magic King")
            maleksehr1()
        if self.checkBox_14.isChecked() == True:
            self.label.setText("Open the jewelry from the Bag")
            jwaher()
        else:
            pass
    def Stoppro(self):
        print('stopppppppp')
        self.thread_manager.stop(self.Tolls_mohemat)
    def Mohemat(self):
        #self.f_pop()
        print('mohematttttttttt')
        # start games
        loop_start = 0
        loop_stop = len(records)
        Geft_Day1 = self.checkBox.isChecked()
        open_gift1 = self.checkBox_2.isChecked()
        Hebet_afret1 = self.checkBox_3.isChecked()
        Tagned_Abtal1 = self.checkBox_4.isChecked()
        #ahgar1 = self.checkBox_5.isChecked()
        mohmat_aldobat1 = self.checkBox_6.isChecked()
        Naforah1 = self.checkBox_7.isChecked()
        fild_B = self.checkBox_8.isChecked()
        open_Box_M = self.checkBox_10.isChecked()  # فتح صناديق المهمات
        speed_farm_M = self.checkBox_11.isChecked()  # مهمة الجمع السريع
        geft_estetla_M = self.checkBox_12.isChecked()  # مهمة هدايا برج الاستطلاع
        defind3Day = self.checkBox_13.isChecked()       #حماية 3 ايام
        almanganek_M = self.checkBox_15.isChecked()  # المنجنيق
        fild_in_cal = self.checkBox_16.isChecked()  #الجمع من الداخل
        #start_gg(ver_g, timeout=200)     #تشغيل العبة بوقت

        # المهمات جميعها
        #####################################################################################
        def mohemat_all1():
            self.time1 = datetime.now()
            print('mohemat alllllllll')
            if Geft_Day1 == True:          #المهام اليومية زر
                if self.time1.hour == 5:
                    self.label.setText("المهمات اليومية H5")
                    point()
                elif self.time1.hour == 6:
                    self.label.setText("المهمات اليومية H6")
                    point()
                elif self.time1.hour == 7:
                    self.label.setText("المهمات اليومية H7")
                    point()
                elif self.time1.hour == 20:
                    self.label.setText("المهمات اليومية H20")
                    point()
            if open_gift1 == True:
                self.label.setText("فتح الهدايا اليومية ")
                Geft_Day()
            if geft_estetla_M == True:
                self.label.setText("فتح هدايا برج الاستطلاع")
                gift_estetla()
            if Hebet_afret1 == True:
                if self.time1.hour == 8:
                    self.label.setText("هبة العفريت H8")
                    Hebet_afret()
                elif self.time1.hour == 9:
                    self.label.setText("هبة العفريت H8")
                    Hebet_afret()
                elif self.time1.hour == 10:
                    self.label.setText("هبة العفريت H10")
                    Hebet_afret()
                elif self.time1.hour == 20:
                    self.label.setText("هبة العفريت H20")
                    Hebet_afret()
            if fild_in_cal == True:
                self.label.setText("الجمع من الداخل")
                fild_in_in()
            ######################################
            if almanganek_M == True:
                if self.time1.hour == 5:
                    self.label.setText("المنجنيق H5")
                    almanganek()
                if self.time1.hour == 6:
                    self.label.setText("المنجنيق H6")
                    almanganek()
            if Tagned_Abtal1 == True:
                self.label.setText("تجنيد الابطال")
                Tagned_Abtal()
            if mohmat_aldobat1 == True:
                self.label.setText("مهمات الضباط WARPATH")
                mohmat_aldobat()
            if Naforah1 == True:
                if self.time1.hour == 11:
                    self.label.setText("النافورة السحرية H11")
                    Naforah()
                elif self.time1.hour == 12:
                    self.label.setText("النافورة السحرية H12")
                    Naforah()
                elif self.time1.hour == 13:
                    self.label.setText("النافورة السحرية H13")
                    Naforah()

            # فتح الصناديق المهمات
            if open_Box_M == True:
                if self.time1.hour == 21:
                    self.label.setText("فتح صناديق المهمات H21")
                    openbox()
                elif self.time1.hour == 22:
                    self.label.setText("فتح صناديق المهمات H22")
                    openbox()
                elif self.time1.hour == 23:
                    self.label.setText("فتح صناديق المهمات H23")
                    openbox()
            # مهمة الجمع السريع
            if speed_farm_M == True:
                self.label.setText("الجمع السريع")
                speedFarm()
            #حماية ثلاث ايام
            if defind3Day == True:
                self.label.setText("حماية لمدة 3 ايام")
                defind()

            #####################################################################################
            #ahgar1


            ###################################################
            if fild_B == True:

                ###Farm1_Fild
                if loop_start == 1:
                    self.label.setText("ارسال الى الحقول")
                    if records[0][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()

                    if records[0][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()

                    if records[0][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()

                    if records[0][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()

                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm2_Fild
                if loop_start == 2:
                    self.label.setText("ارسال الى الحقول")
                    if records[1][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[1][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[1][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[1][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm3_Fild
                if loop_start == 3:
                    self.label.setText("ارسال الى الحقول")
                    if records[2][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[2][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[2][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[2][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm4_Fild
                if loop_start == 4:
                    self.label.setText("ارسال الى الحقول")
                    if records[3][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[3][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[3][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[3][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm5_Fild
                if loop_start == 5:
                    self.label.setText("ارسال الى الحقول")
                    if records[4][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[4][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[4][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[4][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm6_Fild
                if loop_start == 6:
                    self.label.setText("ارسال الى الحقول")
                    if records[5][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[5][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[5][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[5][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm7_Fild
                if loop_start == 7:
                    self.label.setText("ارسال الى الحقول")
                    if records[6][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[6][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[6][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[6][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 8:
                    self.label.setText("ارسال الى الحقول")
                    if records[7][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[7][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[7][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[7][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 9:
                    self.label.setText("ارسال الى الحقول")
                    if records[8][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[8][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[8][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[8][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 10:
                    self.label.setText("ارسال الى الحقول")
                    if records[9][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[9][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[9][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[9][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 11:
                    self.label.setText("ارسال الى الحقول")
                    if records[10][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[10][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[10][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[10][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 12:
                    self.label.setText("ارسال الى الحقول")
                    if records[11][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[11][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[11][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[11][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 13:
                    self.label.setText("ارسال الى الحقول")
                    if records[12][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[12][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[12][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[12][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 14:
                    self.label.setText("ارسال الى الحقول")
                    if records[13][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[13][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[13][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[13][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 15:
                    self.label.setText("ارسال الى الحقول")
                    if records[14][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[14][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[14][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[14][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 16:
                    self.label.setText("ارسال الى الحقول")
                    if records[15][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[15][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[15][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[15][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 17:
                    self.label.setText("ارسال الى الحقول")
                    if records[16][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[16][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[16][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[16][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 18:
                    self.label.setText("ارسال الى الحقول")
                    if records[17][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[17][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[17][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[17][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 19:
                    self.label.setText("ارسال الى الحقول")
                    if records[18][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[18][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[18][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[18][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 20:
                    self.label.setText("ارسال الى الحقول")
                    if records[19][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[19][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[19][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[19][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 21:
                    self.label.setText("ارسال الى الحقول")
                    if records[20][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[20][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[20][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[20][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 22:
                    self.label.setText("ارسال الى الحقول")
                    if records[21][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[21][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[21][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[21][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 23:
                    self.label.setText("ارسال الى الحقول")
                    if records[22][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[22][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[22][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[22][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 24:
                    self.label.setText("ارسال الى الحقول")
                    if records[23][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[23][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[23][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[23][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 25:
                    self.label.setText("ارسال الى الحقول")
                    if records[24][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[24][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[24][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[24][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 26:
                    self.label.setText("ارسال الى الحقول")
                    if records[25][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[25][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[25][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[25][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 27:
                    self.label.setText("ارسال الى الحقول")
                    if records[26][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[26][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[26][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[26][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 28:
                    self.label.setText("ارسال الى الحقول")
                    if records[27][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[27][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[27][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[27][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 29:
                    self.label.setText("ارسال الى الحقول")
                    if records[28][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[28][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[28][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[28][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 30:
                    self.label.setText("ارسال الى الحقول")
                    if records[29][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[29][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[29][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[29][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 31:
                    self.label.setText("ارسال الى الحقول")
                    if records[30][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[30][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[30][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[30][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 32:
                    self.label.setText("ارسال الى الحقول")
                    if records[31][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[31][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[31][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[31][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 33:
                    self.label.setText("ارسال الى الحقول")
                    if records[32][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[32][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[32][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[32][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 34:
                    self.label.setText("ارسال الى الحقول")
                    if records[33][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[33][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[33][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[33][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 35:
                    self.label.setText("ارسال الى الحقول")
                    if records[34][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[34][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[34][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[34][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 36:
                    self.label.setText("ارسال الى الحقول")
                    if records[35][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[35][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[35][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[35][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 37:
                    self.label.setText("ارسال الى الحقول")
                    if records[36][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[36][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[36][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[36][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 38:
                    self.label.setText("ارسال الى الحقول")
                    if records[37][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[37][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[37][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[37][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 39:
                    self.label.setText("ارسال الى الحقول")
                    if records[38][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[38][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[38][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[38][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 40:
                    self.label.setText("ارسال الى الحقول")
                    if records[39][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[39][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[39][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[39][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 41:
                    self.label.setText("ارسال الى الحقول")
                    if records[40][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[40][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[40][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[40][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 42:
                    self.label.setText("ارسال الى الحقول")
                    if records[41][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[41][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[41][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[41][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 43:
                    self.label.setText("ارسال الى الحقول")
                    if records[42][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[42][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[42][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[42][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 44:
                    self.label.setText("ارسال الى الحقول")
                    if records[43][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[43][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[43][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[43][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")

                ###Farm_Fild
                if loop_start == 45:
                    self.label.setText("ارسال الى الحقول")
                    if records[44][3] == 'Wheat':
                        self.label_4.setText("جمع قمح")
                        wheat_1()
                    if records[44][3] == 'Wood':
                        self.label_4.setText("جمع خشب")
                        Wood_1()
                    if records[44][3] == 'Iron':
                        self.label_4.setText("جمع حديد")
                        iron_1()
                    if records[44][3] == 'Silvar':
                        self.label_4.setText("جمع فضة")
                        silvar_1()
                    self.label.setText("الانتهاء و الذهاب للمزرعة التالية")
        #####################################################################################
        while loop_start <= loop_stop:
            loop_start += 1
            if loop_start == loop_stop:
                loop_start -= loop_stop

            ###############loop####################
            if loop_start == 1:
                print('farm1')
                # #######اغلاق البداية
                #sleep(5)
                if exists(close_1):
                    self.label.setText("اغلاق بداية العبة")
                    close_first()
                    sleep()
                if exists(back_shahen):
                    touch(back_shahen)
                self.label_2.setText(records[0][1])
                login_farm1()
                mohemat_all1()
                self.label.setText("ُEnd Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 2:
                print('farm2')
                self.label_2.setText(records[1][1])
                login_farm2()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 3:
                self.label_2.setText(records[2][1])
                login_farm3()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 4:
                self.label_2.setText(records[3][1])
                login_farm4()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 5:
                self.label_2.setText(records[4][1])
                login_farm5()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 6:
                self.label_2.setText(records[5][1])
                login_farm6()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 7:
                self.label_2.setText(records[6][1])
                login_farm7()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 8:
                self.label_2.setText(records[7][1])
                login_farm8()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 9:
                self.label_2.setText(records[8][1])
                login_farm9()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ########################loop#################################
            if loop_start == 10:
                self.label_2.setText(records[9][1])
                login_farm10()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 11:
                self.label_2.setText(records[10][1])
                login_farm11()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 12:
                self.label_2.setText(records[11][1])
                login_farm12()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 13:
                self.label_2.setText(records[12][1])
                login_farm13()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 14:
                self.label_2.setText(records[13][1])
                login_farm14()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 15:
                self.label_2.setText(records[14][1])
                login_farm15()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 16:
                self.label_2.setText(records[15][1])
                login_farm16()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 17:
                self.label_2.setText(records[16][1])
                login_farm17()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 18:
                self.label_2.setText(records[17][1])
                login_farm18()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 19:
                self.label_2.setText(records[18][1])
                login_farm19()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 20:
                self.label_2.setText(records[19][1])
                login_farm20()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 21:
                self.label_2.setText(records[20][1])
                login_farm21()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 22:
                self.label_2.setText(records[21][1])
                login_farm22()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 23:
                self.label_2.setText(records[22][1])
                login_farm23()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 24:
                self.label_2.setText(records[23][1])
                login_farm24()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 25:
                self.label_2.setText(records[24][1])
                login_farm25()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 26:
                self.label_2.setText(records[25][1])
                login_farm26()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 27:
                self.label_2.setText(records[26][1])
                login_farm27()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 28:
                self.label_2.setText(records[27][1])
                login_farm28()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 29:
                self.label_2.setText(records[28][1])
                login_farm29()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 30:
                self.label_2.setText(records[29][1])
                login_farm30()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 31:
                self.label_2.setText(records[30][1])
                login_farm31()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 32:
                self.label_2.setText(records[31][1])
                login_farm32()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 33:
                self.label_2.setText(records[32][1])
                login_farm33()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 34:
                self.label_2.setText(records[33][1])
                login_farm34()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 35:
                self.label_2.setText(records[34][1])
                login_farm35()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 36:
                self.label_2.setText(records[35][1])
                login_farm36()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 37:
                self.label_2.setText(records[36][1])
                login_farm37()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 38:
                self.label_2.setText(records[37][1])
                login_farm38()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 39:
                self.label_2.setText(records[38][1])
                login_farm39()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 40:
                self.label_2.setText(records[39][1])
                login_farm40()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 41:
                self.label_2.setText(records[40][1])
                login_farm41()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 42:
                self.label_2.setText(records[41][1])
                login_farm42()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 43:
                self.label_2.setText(records[42][1])
                login_farm43()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 44:
                self.label_2.setText(records[43][1])
                login_farm44()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
            ######################################################################################
            if loop_start == 45:
                self.label_2.setText(records[44][1])
                login_farm45()
                mohemat_all1()
                self.label.setText("End Task Go To Next Farm")
                self.Tolls_mohemat()
        ######################################################################################

    def Button_programs(self):
        print('start programs')
        self.start_bot.clicked.connect(self.thred_1)
        self.StartTools_pushButton_2.clicked.connect(self.thred_3)
        self.add_farm_m2_pushButton.clicked.connect(self.go_MainFarm)
        self.pushButton.clicked.connect(self.Login_next)
        self.pushButton_2.clicked.connect(self.th_defind)

    def exit_click(self):
        print('exit click')
        self.exit_bot.clicked.connect(self.thred_2)

        # stop_app("com.and.riseofthekings")  # ....ايقاف اللعبة

    def LoginMainFarm(self):
        print('LoginMainFarm')
        self.window = QtWidgets.QDialog()
        self.ui = Ui_mainFarm_D()
        self.ui.setupUi(self.window)
        self.window.show()

    def go_MainFarm(self):
        print('go_MainFarm')
        login1 = MainFarm()
        widget.addWidget(login1)
        widget.setCurrentIndex(widget.currentIndex() + 1)


class MainFarm(QDialog):
    def __init__(self):
        super(MainFarm, self).__init__()
        loadUi("mainFarm.ui", self)

        self.Load_Database()

        self.back_pushButton.clicked.connect(self.go_meapp)
        self.delet_pushButton_2.clicked.connect(self.Delete_Data)
        self.Main_Edit_pushButton_3.clicked.connect(self.go_Edit_Dilog)
        self.add_add1_pushButton.clicked.connect(self.add1_data)


    def add1_data(self):
        # Pop up Box
        msg = QMessageBox()
        msg.setWindowTitle("Save To Database!!!")
        msg.setText("Save Data in DB")
        msg.setIcon(QMessageBox.Information)
        x = msg.exec_()

        add_user_Farm = self.User_Name_lineEdit.text()
        add_Password = self.Password_lineEdit.text()
        add_TaibFild = self.Taib_Fild_comboBox.currentText()
        add_number_Marsh = self.Number_Marsh_comboBox.currentText()
        add_Lvl_Fild = self.Lvl_Fild_comboBox.currentText()

        try:
            curs.execute(
                'INSERT INTO information (User_Name,U_PASSWORD,Taib_Fild,Number_Marsh,lvl_Fild)VALUES (?,?,?,?,?)',
                (add_user_Farm, add_Password, add_TaibFild, add_number_Marsh, add_Lvl_Fild))
            conn.commit()
            print("Done")
            self.Load_Database()
        except Exception as error:
            print(error)
        # clear item box in line
        # self.User_Name_lineEdit.setText("")
        # self.Password_lineEdit.setText("")
        self.User_Name_lineEdit.clear()
        self.Password_lineEdit.clear()

    def Load_Database(self):
        while self.tableWidget.rowCount() > 0:
            self.tableWidget.removeRow(0)
        conn = sqlite3.connect('farm.db')
        content = 'SELECT * FROM information'
        res = conn.execute(content)

        for row_index, row_data in enumerate(res):
            self.tableWidget.insertRow(row_index)
            for colm_index, colm_data in enumerate(row_data):
                self.tableWidget.setItem(row_index, colm_index, QTableWidgetItem(str(colm_data)))
        self.Total_User_label_2.setText("Total User Farm : " + str(self.tableWidget.rowCount()))

        # conn.close()
        return

    def Delete_Data(self):
        content = 'SELECT * FROM information'
        res = conn.execute(content)
        for row in enumerate(res):
            if row[0] == self.tableWidget.currentRow():
                data = row[1]
                add_id = data[0]
                # add_user_Farm = data[1]
                # add_Password = data[2]
                # add_TaibFild = data[3]
                # add_number_Marsh = data[4]
                # add_Lvl_Fild = data[5]
                # add_Taib_ston = data[6]
                # curs.execute("DELETE FROM information WHERE User_Name=? AND U_PASSWORD=? AND Taib_Fild=? AND Number_Marsh=? AND lvl_Fild=? AND Taib_of_Ston=?", (add_user_Farm, add_Password, add_TaibFild, add_number_Marsh, add_Lvl_Fild, add_Taib_ston,))
                curs.execute(("DELETE FROM information WHERE id=?"), (add_id,))  # في حال وجوداي دي
                conn.commit()
                self.Load_Database()
                name_table = "information"
                curs.execute("UPDATE sqlite_sequence SET seq=0 WHERE name=? ", (name_table,))
                conn.commit()


    def go_meapp(self):
        login = meapp()
        widget.addWidget(login)
        widget.setCurrentIndex(widget.currentIndex() + 1)

    def go_Edit_Dilog(self):
        login4 = Edit2Dilog()
        widget.addWidget(login4)
        widget.setCurrentIndex(widget.currentIndex() + 1)


class Edit2Dilog(QDialog):

    def __init__(self):
        super(Edit2Dilog, self).__init__()
        loadUi("editDilog.ui", self)
        self.Back_Edit_pushButton.clicked.connect(self.go_BB_MainFarm)
        self.Edit_Add_pushButton.clicked.connect(self.Edit_data)

    def Edit_data(self):
        try:
            conn1 = sqlite3.connect('farm.db')
            curs1 = conn.cursor()

            ID_ID = self.ID_lineEdit.text()
            edit_user_Farm = self.edit_Usename_lineEdit.text()
            edit_Password = self.Edit_Password_lineEdit_2.text()
            edit_TaibFild = self.Edit_Taib_Fild_comboBox.currentText()
            edit_number_Marsh = self.Edit_Number_Marsh_comboBox.currentText()
            edit_Lvl_Fild = self.Edit_Lvl_Fild_comboBox.currentText()
            edit_Taib_ston = self.Edit_Taib_ston_comboBox.currentText()
            updateq = "UPDATE information SET U_PASSWORD = ?,Taib_Fild = ?,Number_Marsh = ?,lvl_Fild = ?,Taib_of_Ston = ? WHERE User_Name = ?"
            values = (ID_ID, edit_user_Farm, edit_Password, edit_TaibFild, edit_number_Marsh, edit_Lvl_Fild, edit_Lvl_Fild, edit_Taib_ston)
            if ID_ID == "" and edit_user_Farm == "" and edit_Password == "" and edit_TaibFild == "" and edit_number_Marsh == "" and edit_Lvl_Fild == "" and edit_Taib_ston == "":
                curs1.execute(updateq, values)
                conn1.commit()
                print('add scsecc')
        except:
            print("fuk you")


    def go_BB_MainFarm(self):
        loginB2 = MainFarm()
        widget.addWidget(loginB2)
        widget.setCurrentIndex(widget.currentIndex() + 1)


app = QApplication(sys.argv)
welcom = loginM()
widget = QtWidgets.QStackedWidget()
widget.addWidget(welcom)
widget.setFixedHeight(800)
# widget.setFixedWidth(400)
widget.setWindowTitle("بوت المزارع")
widget.show()

try:
    sys.exit(app.exec_())
except:
    print("Exiting")
