# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
auto_setup(__file__)
#from airtest.core.android.touch_methods.base_touch import *
import os
import sys

import random
import threading
import time



#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"

#init_device("Android", ime_method="ADBIME")
dev = device()
#dev.yosemite_ime.code("2")

###################################################################################
using(r"image/close")
using(r"config/farm")

from close import *

#os.system('adb_in.cmd')

#from Login_user import *
#shell("input text 'em11.strip()")
####################################################################
Kwheat = (998, 1515)
KWood = (797, 1504)
Kiron = (629, 1490)
Ksilver = (450, 1501)
Rwheeat = Template(r"tpl1666764611816.png", record_pos=(0.413, 0.6), resolution=(1080, 1920))
Rwood = Template(r"tpl1666764739826.png", record_pos=(0.247, 0.599), resolution=(1080, 1920))
Riron = Template(r"tpl1666764773312.png", record_pos=(0.085, 0.6), resolution=(1080, 1920))
Rsilvar = Template(r"tpl1666764804458.png", record_pos=(-0.079, 0.599), resolution=(1080, 1920))

back_shahen2 = Template(r"tpl1656511974558.png", threshold=0.8, record_pos=(-0.431, -0.849), resolution=(1080, 1920))
masera_top = Template(r"tpl1664614151590.png", threshold=0.95, record_pos=(-0.001, -0.844), resolution=(1080, 1920))
go_maserah = Template(r"tpl1666761712625.png", threshold=0.8, record_pos=(-0.242, 0.812), resolution=(1080, 1920))
out_first = Template(r"tpl1664548626551.png", threshold=0.95, record_pos=(0.408, -0.546), resolution=(1080, 1920))
no_hav_solger = Template(r"tpl1665771738735.png", threshold=0.95, record_pos=(0.002, -0.328), resolution=(1080, 1920))
no_hav_solger2 = Template(r"tpl1665993432452.png", threshold=0.95, record_pos=(0.131, -0.191), resolution=(1080, 1920))

baheth = Template(r"tpl1658601525206.png", threshold=0.7, record_pos=(0.425, 0.303), resolution=(1080, 1920))
infildnomber = Template(r"tpl1666963741353.png", target_pos=4, record_pos=(0.435, 0.7), resolution=(1080, 1920))
go_to = Template(r"tpl1661689421421.png", threshold=0.8, record_pos=(-0.344, 0.825), resolution=(1080, 1920))
algame3 = Template(r"tpl1658599372704.png", threshold=0.7, record_pos=(0.201, 0.001), resolution=(1080, 1920))
massegEntabeh = Template(r"tpl1665173073256.png", threshold=0.8, record_pos=(0.107, -0.189), resolution=(1080, 1920))
el3a2 = Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920))
taked = Template(r"tpl1666000456317.png", threshold=0.95, record_pos=(0.002, 0.139), resolution=(1080, 1920))
vip_be = Template(r"tpl1667168966926.png", record_pos=(-0.194, -0.265), resolution=(1080, 1920))

alhogomala = Template(r"tpl1665934683552.png", threshold=0.80, record_pos=(0.246, 0.014), resolution=(1080, 1920))
alsharh = Template(r"tpl1666681419297.png", threshold=0.80, record_pos=(-0.171, 0.173), resolution=(1080, 1920))
alwhesh = Template(r"tpl1665987076389.png", threshold=0.80, record_pos=(0.005, 0.556), resolution=(1080, 1920))

bahethwahesh = Template(r"tpl1666629471938.png", threshold=0.85, record_pos=(0.001, 0.616), resolution=(1080, 1920))

awdah = Template(r"tpl1666681517926.png", threshold=0.7, record_pos=(0.244, 0.045), resolution=(1080, 1920))


#################################################################################
def alhojoala():
    for x in range(5):
        if x == 5:
            break
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)                    
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            sleep()
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")

def alsharh():
    for x in range(5):
        if x == 5:
            break
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)                    
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            sleep()
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")

def awdah():
    for x in range(5):
        if x == 5:
            break
        
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)                    
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            sleep()
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")               

def wheat_test():
    try:
        if exists(in_kal3ah):
            in_2_aout()

        if exists(baheth):
            touch(baheth)
            sleep()
            if exists(Rsilvar):
                touch(Ksilver)
                touch(infildnomber)
                sleep()
                keyevent("KEYCODE_DEL")
                sleep()
                text('6')
                touch(go_to)
            for x in range(5):
                if x == 5:
                    break
                if exists(baheth):
                    touch(baheth)
                    sleep()
                    touch(go_to)
                    if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                        print("omgggggggggggggggggggggggggggggggggggggggggggggggggg")
                        break

                else:
                    print("else baheth")                    
                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)                    
                #sleep()
                touch((541, 946))
                sleep()
                if exists(algame3):
                    touch(algame3)
                    sleep()
                    if exists(go_maserah):
                        touch(go_maserah)
                        sleep()
                        if exists(no_hav_solger2):
                            print('no solger else')
                            touch([550, 1117])
                            sleep()
                            touch(back_shahen2)
                            break
                else:
                    print("else algame3")
                    if exists(go_maserah):
                        touch(go_maserah)
                    if exists(alhogomala):
                        alhojoala()

                    if exists(awdah):
                        awdah()                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)
                    if exists(massegEntabeh):
                        touch(el3a2)
                        break
                    if exists(no_hav_solger2):
                        print('no solger else')
                        touch([550, 1117])
                        sleep()
                        touch(back_shahen2)
                        break

                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    print("else fild wilt")  

    except:
        print('except lop ')
        youm_alhath()

#th = threading.Thread(target=wheat_test)
#th.start()
#th.join()

def testWait():
    try:
        wait(baheth, timeout=4)
    except:
        print("no hav go masera")

def writTesxt():
    pass
    #start_app("com.netease.nie.yosemite")
    #start_app("com.sohu.inputmethod.sogou.xiaomi/.SogouIME")
    #start_app("com.netease.nie.yosemite/.ime.ImeService")
    #print(shell("ls"))

    #shell("ime enable com.netease.nie.yosemite/.ime.ImeService")
    #shell("ime set com.netease.nie.yosemite/.ime.ImeService")
    
def dajag():
    if exists(Template(r"tpl1669191064232.png", record_pos=(-0.002, 0.544), resolution=(1080, 1920))):
        touch(Template(r"tpl1669191064232.png", record_pos=(-0.002, 0.544), resolution=(1080, 1920)))
        while True:
            if exists(Template(r"tpl1669191709416.png", threshold=0.95, record_pos=(0.142, -0.375), resolution=(1080, 1920))):
                touch(Template(r"tpl1669191229416.png", record_pos=(0.003, 0.273), resolution=(1080, 1920)))
    
ym = "com.sohu.inputmethod.sogouoem/.SogouIME"
sh = "com.netease.nie.yosemite/.ime.ImeService"

def set_ime(ime):
    shell("ime enable " + ime)
    shell("ime set " + ime)

set_ime(ym)

text("123", enter=False)

set_ime(sh)