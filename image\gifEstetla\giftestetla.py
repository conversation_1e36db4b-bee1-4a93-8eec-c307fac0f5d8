# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"

###################################################################################
using(r"image/close")
from close import *


def gift_estetla():
    try:
       
        if exists(Template(r"tpl1682204051314.png", target_pos=1, record_pos=(-0.262, -0.453), resolution=(1080, 1920))):
            touch(Template(r"tpl1682204051314.png", target_pos=1, record_pos=(-0.262, -0.453), resolution=(1080, 1920)))
            sleep()
            
            touch([923, 939])
            sleep()
            
            

            if exists(Template(r"tpl1666185781209.png", threshold=0.95, record_pos=(0.007, 0.307), resolution=(1080, 1920))):
                touch(Template(r"tpl1666185781209.png", threshold=0.95, record_pos=(0.007, 0.307), resolution=(1080, 1920)))
                wait(Template(r"tpl1666194036633.png", threshold=0.85, record_pos=(-0.002, -0.247), resolution=(1080, 1920)))
                touch([548, 1359])
                sleep()
                touch([79, 48])
                
            else:
                if exists(Template(r"tpl1682205007053.png", record_pos=(0.076, 0.269), resolution=(1080, 1920))):
                    touch([137, 188])
                    sleep()
                    touch([76, 55])
                
                
        else:
            print("no find")
            
    except:
        youm_alhath()
