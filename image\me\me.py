# -*- encoding=utf8 -*-
__author__ = "fofo"



from airtest.core.api import *
from airtest.cli.parser import cli_setup

from datetime import datetime
import datetime as dt
#import datetime
# import random

import time
import sys
import os
from os import path
import pyrebase
import sqlite3

ST.PROJECT_ROOT = "C:\\bootGames"
using(r"image/close")
using(r"config/farm")
from close import *
from userfarm import *

def jop():
    print('تشغيل me')


def sch1_1():

    schedule.every().day.at("03:40").do(jop)























