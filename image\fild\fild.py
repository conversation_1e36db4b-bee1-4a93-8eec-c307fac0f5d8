# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time

# if not cli_setup():
# auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555", ], project_root="C:/bootGames")

ST.PROJECT_ROOT = "C:\\bootGames"

###################################################################################
using(r"image/close")
using(r"config/farm")
from close import *
from userfarm import *

#############################################################
Kwheat = (998, 1515)
KWood = (797, 1504)
Kiron = (629, 1490)
Ksilver = (450, 1501)
Rwheeat = Template(r"tpl1666764611816.png", record_pos=(0.413, 0.6), resolution=(1080, 1920))
Rwood = Template(r"tpl1666764739826.png", record_pos=(0.247, 0.599), resolution=(1080, 1920))
Riron = Template(r"tpl1666764773312.png", record_pos=(0.085, 0.6), resolution=(1080, 1920))
Rsilvar = Template(r"tpl1666764804458.png", record_pos=(-0.079, 0.599), resolution=(1080, 1920))

back_shahen2 = Template(r"tpl1656511974558.png", threshold=0.8, record_pos=(-0.431, -0.849), resolution=(1080, 1920))
masera_top = Template(r"tpl1664614151590.png", threshold=0.95, record_pos=(-0.001, -0.844), resolution=(1080, 1920))
go_maserah = Template(r"tpl1666761712625.png", threshold=0.7, record_pos=(-0.242, 0.812), resolution=(1080, 1920))
out_first = Template(r"tpl1664548626551.png", threshold=0.95, record_pos=(0.408, -0.546), resolution=(1080, 1920))
no_hav_solger = Template(r"tpl1665771738735.png", threshold=0.95, record_pos=(0.002, -0.328), resolution=(1080, 1920))
no_hav_solger2 = Template(r"tpl1665993432452.png", threshold=0.95, record_pos=(0.131, -0.191), resolution=(1080, 1920))

baheth = Template(r"tpl1658601525206.png", threshold=0.7, record_pos=(0.425, 0.303), resolution=(1080, 1920))
infildnomber = Template(r"tpl1682168334334.png", target_pos=4, record_pos=(0.444, 0.708), resolution=(1080, 1920))
go_to = Template(r"tpl1661689421421.png", threshold=0.8, record_pos=(-0.344, 0.825), resolution=(1080, 1920))
algame3 = Template(r"tpl1658599372704.png", threshold=0.7, record_pos=(0.201, 0.001), resolution=(1080, 1920))
massegEntabeh = Template(r"tpl1665173073256.png", threshold=0.8, record_pos=(0.107, -0.189), resolution=(1080, 1920))
el3a2 = Template(r"tpl1665173096545.png", threshold=0.95, record_pos=(-0.222, 0.138), resolution=(1080, 1920))
taked = Template(r"tpl1666000456317.png", threshold=0.95, record_pos=(0.002, 0.139), resolution=(1080, 1920))
vip_be = Template(r"tpl1667168966926.png", record_pos=(-0.194, -0.265), resolution=(1080, 1920))

alhogomala = Template(r"tpl1665934683552.png", threshold=0.85, record_pos=(0.246, 0.014), resolution=(1080, 1920))
alsharh = Template(r"tpl1666681419297.png", threshold=0.85, record_pos=(-0.171, 0.173), resolution=(1080, 1920))
alwhesh = Template(r"tpl1665987076389.png", threshold=0.95, record_pos=(0.005, 0.556), resolution=(1080, 1920))

bahethwahesh = Template(r"tpl1666629471938.png", threshold=0.95, record_pos=(0.001, 0.616), resolution=(1080, 1920))

awdah = Template(r"tpl1666681517926.png", threshold=0.85, record_pos=(0.244, 0.045), resolution=(1080, 1920))

da3m_alkwat = Template(r"tpl1668150851166.png", record_pos=(0.006, -0.57), resolution=(1080, 1920))


#################################################################################
def alhojoala():
    for x in range(5):
        if x == 5:
            break
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
            if exists(da3m_alkwat):
                touch(el3a2)
                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(da3m_alkwat):
                touch(el3a2)
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            try:
                wait(go_maserah, timeout=4)
            except:
                print("no hav go masera")
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()
            if exists(da3m_alkwat):
                touch(el3a2)

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")
def alsharh():
    for x in range(5):
        if x == 5:
            break
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
            if exists(da3m_alkwat):
                touch(el3a2)
                
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)                    
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            try:
                wait(go_maserah, timeout=4)
            except:
                print("no hav go masera")
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()
            if exists(da3m_alkwat):
                touch(el3a2)

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")
def awdah():
    for x in range(5):
        if x == 5:
            break
        
        if exists(baheth):
            touch(baheth)
            sleep()
            touch(infildnomber)
            sleep()
            keyevent("KEYCODE_DEL")
            keyevent("KEYCODE_DEL")
            sleep()
            text('5')
            touch(go_to)
            if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                print("awwwwwwwwwwdddddddddddddddddddaaaaaaaaaaaaaaaaaaaaaaaa")
                break
        else:
            print("else baheth")                    
            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
            if exists(da3m_alkwat):
                touch(el3a2)
                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)                    
        sleep()
        touch((541, 946))
        sleep()
        if exists(algame3):
            touch(algame3)
            try:
                wait(go_maserah, timeout=4)
            except:
                print("no hav go masera")            
            if exists(go_maserah):
                touch(go_maserah)
                sleep()
                if exists(no_hav_solger2):
                    print('no solger else')
                    tuch([550, 1117])
                    sleep()
                    touch(back_shahen2)
                    break
        else:
            print("else algame3")
            if exists(go_maserah):
                touch(go_maserah)
            if exists(alhogomala):
                alhojoala()
            if exists(da3m_alkwat):
                touch(el3a2)

            if exists(awdah):
                awdah()                    
            if exists(alwhesh):
                touch([410, 445])
            if exists(bahethwahesh):
                touch([496, 398])
            if exists(out_first):
                touch(out_first)
            if exists(massegEntabeh):
                touch(el3a2)
                break
            if exists(no_hav_solger2):
                print('no solger else')
                touch([550, 1117])
                sleep()
                touch(back_shahen2)
                break

            if exists(taked):
                touch(taked)
                break
            if exists(vip_be):
                touch((557, 1410))
                break
                print("else fild wilt")               

def wheat_1():
    try:

        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        if exists(in_kal3ah):
            in_2_aout()
        if exists(baheth):
            touch(baheth)
            sleep()
            if exists(Rwheeat):
                touch(Kwheat)
                touch(infildnomber)
                sleep()
                keyevent("KEYCODE_DEL")
                keyevent("KEYCODE_DEL")
                sleep()
                if records[0][0] == 1:
                    text(records[0][5])
                elif records[1][0] == 2:
                    text(records[1][5])
                elif records[2][0] == 3:
                    text(records[2][5])
                elif records[3][0] == 4:
                    text(records[3][5])
                elif records[4][0] == 5:
                    text(records[4][5])
                elif records[5][0] == 6:
                    text(records[5][5])
                elif records[6][0] == 7:
                    text(records[6][5])
                elif records[7][0] == 8:
                    text(records[7][5])
                elif records[8][0] == 9:
                    text(records[8][5])
                elif records[9][0] == 10:
                    text(records[9][5])
                elif records[10][0] == 11:
                    text(records[10][5])
                elif records[11][0] == 12:
                    text(records[11][5])
                elif records[12][0] == 13:
                    text(records[12][5])
                elif records[13][0] == 14:
                    text(records[13][5])
                elif records[14][0] == 15:
                    text(records[14][5])
                elif records[15][0] == 16:
                    text(records[15][5])
                elif records[16][0] == 17:
                    text(records[16][5])
                elif records[17][0] == 18:
                    text(records[17][5])
                elif records[18][0] == 19:
                    text(records[18][5])
                elif records[19][0] == 20:
                    text(records[19][5])
                elif records[20][0] == 21:
                    text(records[20][5])
                elif records[21][0] == 22:
                    text(records[21][5])
                elif records[22][0] == 23:
                    text(records[22][5])
                elif records[23][0] == 24:
                    text(records[23][5])
                elif records[24][0] == 25:
                    text(records[24][5])
                elif records[25][0] == 26:
                    text(records[25][5])
                elif records[26][0] == 27:
                    text(records[26][5])
                elif records[27][0] == 28:
                    text(records[27][5])
                elif records[28][0] == 29:
                    text(records[28][5])
                elif records[29][0] == 30:
                    text(records[29][5])
                elif records[30][0] == 31:
                    text(records[30][5])
                elif records[31][0] == 32:
                    text(records[31][5])
                elif records[32][0] == 33:
                    text(records[32][5])
                elif records[33][0] == 34:
                    text(records[33][5])
                elif records[34][0] == 35:
                    text(records[34][5])
                elif records[35][0] == 36:
                    text(records[35][5])
                elif records[36][0] == 37:
                    text(records[36][5])
                elif records[37][0] == 38:
                    text(records[37][5])
                elif records[38][0] == 39:
                    text(records[38][5])
                elif records[39][0] == 40:
                    text(records[39][5])
                elif records[40][0] == 41:
                    text(records[40][5])
                elif records[41][0] == 42:
                    text(records[41][5])
                elif records[42][0] == 43:
                    text(records[42][5])
                elif records[43][0] == 44:
                    text(records[43][5])
                elif records[44][0] == 45:
                    text(records[44][5])
                sleep()
                touch(go_to)
            for x in range(5):
                if x == 5:
                    break
                if exists(baheth):
                    touch(baheth)
                    sleep()
                    touch(go_to)
                    if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                        print("omgggggggggggggggggggggggggggggggggggggggggggggggggg")
                        break

                else:
                    print("else baheth")                    
                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    if exists(da3m_alkwat):
                        touch(el3a2)
                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)                    
                #sleep()
                touch((541, 946))
                sleep()
                if exists(algame3):
                    touch(algame3)
                    try:
                        wait(go_maserah, timeout=4)
                    except:
                        print("no hav go masera")                    
                    if exists(go_maserah):
                        touch(go_maserah)
                        sleep()
                        if exists(no_hav_solger2):
                            print('no solger else')
                            touch([550, 1117])
                            sleep()
                            touch(back_shahen2)
                            break
                else:
                    print("else algame3")
                    if exists(go_maserah):
                        touch(go_maserah)
                    if exists(alhogomala):
                        alhojoala()
                    if exists(da3m_alkwat):
                        touch(el3a2)

                    if exists(awdah):
                        awdah()                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)
                    if exists(massegEntabeh):
                        touch(el3a2)
                        break
                    if exists(no_hav_solger2):
                        print('no solger else')
                        touch([550, 1117])
                        sleep()
                        touch(back_shahen2)
                        break

                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    print("else fild wilt")  

    except:
        print('except lop ')
        youm_alhath()


def Wood_1():
    try:

        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        if exists(in_kal3ah):
            in_2_aout()
        if exists(baheth):
            touch(baheth)
            sleep()
            if exists(Rwood):
                touch(KWood)
                touch(infildnomber)
                sleep()
                keyevent("KEYCODE_DEL")
                keyevent("KEYCODE_DEL")
                sleep()
                if records[0][0] == 1:
                    text(records[0][5])
                elif records[1][0] == 2:
                    text(records[1][5])
                elif records[2][0] == 3:
                    text(records[2][5])
                elif records[3][0] == 4:
                    text(records[3][5])
                elif records[4][0] == 5:
                    text(records[4][5])
                elif records[5][0] == 6:
                    text(records[5][5])
                elif records[6][0] == 7:
                    text(records[6][5])
                elif records[7][0] == 8:
                    text(records[7][5])
                elif records[8][0] == 9:
                    text(records[8][5])
                elif records[9][0] == 10:
                    text(records[9][5])
                elif records[10][0] == 11:
                    text(records[10][5])
                elif records[11][0] == 12:
                    text(records[11][5])
                elif records[12][0] == 13:
                    text(records[12][5])
                elif records[13][0] == 14:
                    text(records[13][5])
                elif records[14][0] == 15:
                    text(records[14][5])
                elif records[15][0] == 16:
                    text(records[15][5])
                elif records[16][0] == 17:
                    text(records[16][5])
                elif records[17][0] == 18:
                    text(records[17][5])
                elif records[18][0] == 19:
                    text(records[18][5])
                elif records[19][0] == 20:
                    text(records[19][5])
                elif records[20][0] == 21:
                    text(records[20][5])
                elif records[21][0] == 22:
                    text(records[21][5])
                elif records[22][0] == 23:
                    text(records[22][5])
                elif records[23][0] == 24:
                    text(records[23][5])
                elif records[24][0] == 25:
                    text(records[24][5])
                elif records[25][0] == 26:
                    text(records[25][5])
                elif records[26][0] == 27:
                    text(records[26][5])
                elif records[27][0] == 28:
                    text(records[27][5])
                elif records[28][0] == 29:
                    text(records[28][5])
                elif records[29][0] == 30:
                    text(records[29][5])
                elif records[30][0] == 31:
                    text(records[30][5])
                elif records[31][0] == 32:
                    text(records[31][5])
                elif records[32][0] == 33:
                    text(records[32][5])
                elif records[33][0] == 34:
                    text(records[33][5])
                elif records[34][0] == 35:
                    text(records[34][5])
                elif records[35][0] == 36:
                    text(records[35][5])
                elif records[36][0] == 37:
                    text(records[36][5])
                elif records[37][0] == 38:
                    text(records[37][5])
                elif records[38][0] == 39:
                    text(records[38][5])
                elif records[39][0] == 40:
                    text(records[39][5])
                elif records[40][0] == 41:
                    text(records[40][5])
                elif records[41][0] == 42:
                    text(records[41][5])
                elif records[42][0] == 43:
                    text(records[42][5])
                elif records[43][0] == 44:
                    text(records[43][5])
                elif records[44][0] == 45:
                    text(records[44][5])
                sleep()
                touch(go_to)
            for x in range(5):
                if x == 5:
                    break
                if exists(baheth):
                    touch(baheth)
                    sleep()
                    touch(go_to)
                    if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                        print("omgggggggggggggggggggggggggggggggggggggggggggggggggg")
                        break

                else:
                    print("else baheth")                    
                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    if exists(da3m_alkwat):
                        touch(el3a2)
                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)                    
                #sleep()
                touch((541, 946))
                sleep()
                if exists(algame3):
                    touch(algame3)
                    try:
                        wait(go_maserah, timeout=4)
                    except:
                        print("no hav go masera")                    
                    if exists(go_maserah):
                        touch(go_maserah)
                        sleep()
                        if exists(no_hav_solger2):
                            print('no solger else')
                            touch([550, 1117])
                            sleep()
                            touch(back_shahen2)
                            break
                else:
                    print("else algame3")
                    if exists(go_maserah):
                        touch(go_maserah)
                    if exists(alhogomala):
                        alhojoala()
                    if exists(da3m_alkwat):
                        touch(el3a2)

                    if exists(awdah):
                        awdah()                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)
                    if exists(massegEntabeh):
                        touch(el3a2)
                        break
                    if exists(no_hav_solger2):
                        print('no solger else')
                        touch([550, 1117])
                        sleep()
                        touch(back_shahen2)
                        break

                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    print("else fild wilt")  

    except:
        print('except lop ')
        youm_alhath()


def iron_1():
    try:

        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        if exists(in_kal3ah):
            in_2_aout()
        if exists(baheth):
            touch(baheth)
            sleep()
            if exists(Riron):
                touch(Kiron)
                touch(infildnomber)
                sleep()
                keyevent("KEYCODE_DEL")
                keyevent("KEYCODE_DEL")
                sleep()
                if records[0][0] == 1:
                    text(records[0][5])
                elif records[1][0] == 2:
                    text(records[1][5])
                elif records[2][0] == 3:
                    text(records[2][5])
                elif records[3][0] == 4:
                    text(records[3][5])
                elif records[4][0] == 5:
                    text(records[4][5])
                elif records[5][0] == 6:
                    text(records[5][5])
                elif records[6][0] == 7:
                    text(records[6][5])
                elif records[7][0] == 8:
                    text(records[7][5])
                elif records[8][0] == 9:
                    text(records[8][5])
                elif records[9][0] == 10:
                    text(records[9][5])
                elif records[10][0] == 11:
                    text(records[10][5])
                elif records[11][0] == 12:
                    text(records[11][5])
                elif records[12][0] == 13:
                    text(records[12][5])
                elif records[13][0] == 14:
                    text(records[13][5])
                elif records[14][0] == 15:
                    text(records[14][5])
                elif records[15][0] == 16:
                    text(records[15][5])
                elif records[16][0] == 17:
                    text(records[16][5])
                elif records[17][0] == 18:
                    text(records[17][5])
                elif records[18][0] == 19:
                    text(records[18][5])
                elif records[19][0] == 20:
                    text(records[19][5])
                elif records[20][0] == 21:
                    text(records[20][5])
                elif records[21][0] == 22:
                    text(records[21][5])
                elif records[22][0] == 23:
                    text(records[22][5])
                elif records[23][0] == 24:
                    text(records[23][5])
                elif records[24][0] == 25:
                    text(records[24][5])
                elif records[25][0] == 26:
                    text(records[25][5])
                elif records[26][0] == 27:
                    text(records[26][5])
                elif records[27][0] == 28:
                    text(records[27][5])
                elif records[28][0] == 29:
                    text(records[28][5])
                elif records[29][0] == 30:
                    text(records[29][5])
                elif records[30][0] == 31:
                    text(records[30][5])
                elif records[31][0] == 32:
                    text(records[31][5])
                elif records[32][0] == 33:
                    text(records[32][5])
                elif records[33][0] == 34:
                    text(records[33][5])
                elif records[34][0] == 35:
                    text(records[34][5])
                elif records[35][0] == 36:
                    text(records[35][5])
                elif records[36][0] == 37:
                    text(records[36][5])
                elif records[37][0] == 38:
                    text(records[37][5])
                elif records[38][0] == 39:
                    text(records[38][5])
                elif records[39][0] == 40:
                    text(records[39][5])
                elif records[40][0] == 41:
                    text(records[40][5])
                elif records[41][0] == 42:
                    text(records[41][5])
                elif records[42][0] == 43:
                    text(records[42][5])
                elif records[43][0] == 44:
                    text(records[43][5])
                elif records[44][0] == 45:
                    text(records[44][5])
                sleep()
                touch(go_to)
            for x in range(5):
                if x == 5:
                    break
                if exists(baheth):
                    touch(baheth)
                    sleep()
                    touch(go_to)
                    if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                        print("omgggggggggggggggggggggggggggggggggggggggggggggggggg")
                        break

                else:
                    print("else baheth")                    
                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    if exists(da3m_alkwat):
                        touch(el3a2)
                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)                    
                #sleep()
                touch((541, 946))
                sleep()
                if exists(algame3):
                    touch(algame3)
                    try:
                        wait(go_maserah, timeout=4)
                    except:
                        print("no hav go masera")                    
                    if exists(go_maserah):
                        touch(go_maserah)
                        sleep()
                        if exists(no_hav_solger2):
                            print('no solger else')
                            touch([550, 1117])
                            sleep()
                            touch(back_shahen2)
                            break
                else:
                    print("else algame3")
                    if exists(go_maserah):
                        touch(go_maserah)
                    if exists(alhogomala):
                        alhojoala()
                    if exists(da3m_alkwat):
                        touch(el3a2)                        

                    if exists(awdah):
                        awdah()                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)
                    if exists(massegEntabeh):
                        touch(el3a2)
                        break
                    if exists(no_hav_solger2):
                        print('no solger else')
                        touch([550, 1117])
                        sleep()
                        touch(back_shahen2)
                        break

                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    print("else fild wilt")  

    except:
        print('except lop ')
        youm_alhath()


def silvar_1():
    try:

        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        if exists(in_kal3ah):
            in_2_aout()
        if exists(baheth):
            touch(baheth)
            sleep()
            if exists(Rsilvar):
                touch(Ksilver)
                touch(infildnomber)
                sleep()
                keyevent("KEYCODE_DEL")
                keyevent("KEYCODE_DEL")
                sleep()
                if records[0][0] == 1:
                    text(records[0][5])
                elif records[1][0] == 2:
                    text(records[1][5])
                elif records[2][0] == 3:
                    text(records[2][5])
                elif records[3][0] == 4:
                    text(records[3][5])
                elif records[4][0] == 5:
                    text(records[4][5])
                elif records[5][0] == 6:
                    text(records[5][5])
                elif records[6][0] == 7:
                    text(records[6][5])
                elif records[7][0] == 8:
                    text(records[7][5])
                elif records[8][0] == 9:
                    text(records[8][5])
                elif records[9][0] == 10:
                    text(records[9][5])
                elif records[10][0] == 11:
                    text(records[10][5])
                elif records[11][0] == 12:
                    text(records[11][5])
                elif records[12][0] == 13:
                    text(records[12][5])
                elif records[13][0] == 14:
                    text(records[13][5])
                elif records[14][0] == 15:
                    text(records[14][5])
                elif records[15][0] == 16:
                    text(records[15][5])
                elif records[16][0] == 17:
                    text(records[16][5])
                elif records[17][0] == 18:
                    text(records[17][5])
                elif records[18][0] == 19:
                    text(records[18][5])
                elif records[19][0] == 20:
                    text(records[19][5])
                elif records[20][0] == 21:
                    text(records[20][5])
                elif records[21][0] == 22:
                    text(records[21][5])
                elif records[22][0] == 23:
                    text(records[22][5])
                elif records[23][0] == 24:
                    text(records[23][5])
                elif records[24][0] == 25:
                    text(records[24][5])
                elif records[25][0] == 26:
                    text(records[25][5])
                elif records[26][0] == 27:
                    text(records[26][5])
                elif records[27][0] == 28:
                    text(records[27][5])
                elif records[28][0] == 29:
                    text(records[28][5])
                elif records[29][0] == 30:
                    text(records[29][5])
                elif records[30][0] == 31:
                    text(records[30][5])
                elif records[31][0] == 32:
                    text(records[31][5])
                elif records[32][0] == 33:
                    text(records[32][5])
                elif records[33][0] == 34:
                    text(records[33][5])
                elif records[34][0] == 35:
                    text(records[34][5])
                elif records[35][0] == 36:
                    text(records[35][5])
                elif records[36][0] == 37:
                    text(records[36][5])
                elif records[37][0] == 38:
                    text(records[37][5])
                elif records[38][0] == 39:
                    text(records[38][5])
                elif records[39][0] == 40:
                    text(records[39][5])
                elif records[40][0] == 41:
                    text(records[40][5])
                elif records[41][0] == 42:
                    text(records[41][5])
                elif records[42][0] == 43:
                    text(records[42][5])
                elif records[43][0] == 44:
                    text(records[43][5])
                elif records[44][0] == 45:
                    text(records[44][5])
                sleep()
                touch(go_to)
            for x in range(5):
                if x == 5:
                    break
                if exists(baheth):
                    touch(baheth)
                    sleep()
                    touch(go_to)
                    
                    if exists(Template(r"tpl1665864177775.png", record_pos=(0.155, -0.439), resolution=(1080, 1920))):
                        print("omgggggggggggggggggggggggggggggggggggggggggggggggggg")
                        break

                else:
                    print("else baheth")                    
                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    if exists(da3m_alkwat):
                        touch(el3a2)
                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)                    
                #sleep()
                touch((541, 946))
                sleep()
                if exists(algame3):
                    touch(algame3)
                    try:
                        wait(go_maserah, timeout=4)
                    except:
                        print("no hav go masera")                    
                    if exists(go_maserah):
                        touch(go_maserah)
                        sleep()
                        if exists(no_hav_solger2):
                            print('no solger else')
                            touch([550, 1117])
                            sleep()
                            touch(back_shahen2)
                            break
                else:
                    print("else algame3")
                    if exists(go_maserah):
                        touch(go_maserah)
                    if exists(alhogomala):
                        alhojoala()
                    if exists(da3m_alkwat):
                        touch(el3a2)

                    if exists(awdah):
                        awdah()                    
                    if exists(alwhesh):
                        touch([410, 445])
                    if exists(bahethwahesh):
                        touch([496, 398])
                    if exists(out_first):
                        touch(out_first)
                    if exists(massegEntabeh):
                        touch(el3a2)
                        break
                    if exists(no_hav_solger2):
                        print('no solger else')
                        touch([550, 1117])
                        sleep()
                        touch(back_shahen2)
                        break

                    if exists(taked):
                        touch(taked)
                        break
                    if exists(vip_be):
                        touch((557, 1410))
                        break
                    print("else fild wilt")  

    except:
        print('except lop ')
        youm_alhath()

