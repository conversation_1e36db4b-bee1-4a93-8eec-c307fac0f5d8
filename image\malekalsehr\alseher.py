# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################

using(r"image/close")
from close import *



def maleksehr1():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        while True:
            if exists(Template(r"tpl1674762060803.png", target_pos=2, record_pos=(-0.266, -0.452), resolution=(1080, 1920))):
                touch(Template(r"tpl1674762060803.png", target_pos=2, record_pos=(-0.266, -0.452), resolution=(1080, 1920)))
                wait(Template(r"tpl1663140497215.png", record_pos=(0.001, 0.793), resolution=(1080, 1920)))
                
                touch(Template(r"tpl1663140497215.png", record_pos=(0.001, 0.793), resolution=(1080, 1920)))
                wait(Template(r"tpl1663140704837.png", record_pos=(-0.243, 0.83), resolution=(1080, 1920)))
                
                touch(Template(r"tpl1663140704837.png", record_pos=(-0.243, 0.83), resolution=(1080, 1920)))
                wait(Template(r"tpl1663140772789.png", record_pos=(0.005, -0.505), resolution=(1080, 1920)))
                if exists(Template(r"tpl1663140794511.png", record_pos=(0.002, 0.501), resolution=(1080, 1920))) or exists(Template(r"tpl1663141715677.png", record_pos=(0.0, 0.496), resolution=(1080, 1920))):
                    touch(Template(r"tpl1663140809734.png", record_pos=(0.005, 0.416), resolution=(1080, 1920)))
                    wait(Template(r"tpl1663140433251.png", record_pos=(-0.27, -0.644), resolution=(1080, 1920)))
                else:
                    break
                
            
    except:
        print('axcept malek alsehr')
        youm_alhath()

