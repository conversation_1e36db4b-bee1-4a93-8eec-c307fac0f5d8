# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
#import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################

hakebah =  Template(r"tpl1668187790425.png", record_pos=(-0.107, 0.829), resolution=(1080, 1920))



def jwaher():
    try:
        if exists(back_shahen):
            touch(back_shahen)

        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        if exists(hakebah):
            touch(hakebah)
            try:
                wait(Template(r"tpl1668187884724.png", record_pos=(0.001, -0.847), resolution=(1080, 1920)),timeout=4)
            except:
                pass
            for x in range(10):
                if x == 10:
                    break            
                touch(Template(r"tpl1668188031890.png", record_pos=(0.25, -0.551), resolution=(1080, 1920)))

                if exists(Template(r"tpl1668188102004.png", record_pos=(-0.344, -0.12), resolution=(1080, 1920))):
                    touch(Template(r"tpl1668188102004.png", record_pos=(-0.344, -0.12), resolution=(1080, 1920)))
                    
                    try:
                        wait(Template(r"tpl1668188517327.png", threshold=0.95, record_pos=(-0.305, 0.208), resolution=(1080, 1920)),timeout=4)
                    except:
                        pass
                    
                    if exists(Template(r"tpl1668191363211.png", record_pos=(0.335, -0.149), resolution=(1080, 1920))) or exists(Template(r"tpl1668191646634.png", record_pos=(0.336, -0.154), resolution=(1080, 1920))):
                        touch(Template(r"tpl1668188517327.png", threshold=0.95, record_pos=(-0.305, 0.208), resolution=(1080, 1920)))
                        sleep()

                        if exists(Template(r"tpl1668192586392.png", threshold=0.95, record_pos=(-0.003, 0.184), resolution=(1080, 1920))):
                                                                                    
                            try:
                                wait(Template(r"tpl1668188973608.png", record_pos=(0.335, 0.037), resolution=(1080, 1920)),timeout=4)
                                
                            except:
                                pass
                            swipe(Template(r"tpl1668188973608.png", record_pos=(0.335, 0.037), resolution=(1080, 1920)), vector=[-0.4271, 0.0002])
                            try:
                                wait(Template(r"tpl1668189060703.png", threshold=0.9, record_pos=(-0.105, 0.033), resolution=(1080, 1920)),timeout=5)
                                
                            except:
                                pass
                            if exists(Template(r"tpl1668189060703.png", threshold=0.9, record_pos=(-0.105, 0.033), resolution=(1080, 1920))):
                                touch(Template(r"tpl1668189264126.png", threshold=0.95, record_pos=(0.003, 0.187), resolution=(1080, 1920)))
                                try:
                                    wait(Template(r"tpl1668187884724.png", record_pos=(0.001, -0.847), resolution=(1080, 1920)),timeout=4)
                                except:
                                    pass
                        else:
                            if exists(Template(r"tpl1668189957748.png", threshold=0.95, record_pos=(0.004, 0.136), resolution=(1080, 1920))):
                                touch(Template(r"tpl1668189957748.png", threshold=0.95, record_pos=(0.004, 0.136), resolution=(1080, 1920)))
                                sleep()                            
                    else:
                        touch(back_shahen)
                        break
                else:
                    touch(back_shahen)
                    break
        
    except:
        pass
