# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time


#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")



ST.PROJECT_ROOT = "C:\\bootGames"

###################################################################################
using(r"image/close")
using(r"config/farm")
from close import *
from userfarm import *
########################################



########################################
e3dadat = Template(r"tpl1657005702183.png", threshold=0.85, target_pos=2, record_pos=(0.345, 0.827), resolution=(1080, 1920))
alhesab = Template(r"tpl1657005812094.png", threshold=0.95, target_pos=2, record_pos=(-0.325, -0.424), resolution=(1080, 1920))
t3eeralhesab = Template(r"tpl1684990039483.png", record_pos=(0.008, 0.764), resolution=(1080, 1920))
top_t3eeralhesab = Template(r"tpl1684990180640.png", threshold=0.95, record_pos=(0.297, -0.725), resolution=(1080, 1920))
inemail = Template(r"tpl1744989212523.png", record_pos=(0.179, -0.482), resolution=(1080, 1920))
next_1 = Template(r"tpl1745099483330.png", threshold=0.98, record_pos=(0.001, -0.26), resolution=(1080, 1920))

tsjel_ald5ol = Template(r"tpl1678560947711.png", threshold=0.97, rgb=False, record_pos=(0.005, 0.22), resolution=(1080, 1920))
lakdda5ltalhesab = Template(r"tpl1657006395233.png", threshold=0.8, record_pos=(-0.002, 0.709), resolution=(1080, 1920))
backdo5ol = Template(r"tpl1745086598920.png", record_pos=(0.437, -0.824), resolution=(1080, 1920))
ente_password = Template(r"tpl1684988935051.png", threshold=0.8, record_pos=(0.298, -0.352), resolution=(1080, 1920))
actvate_kepord = Template(r"tpl1745089576251.png", record_pos=(0.003, 0.86), resolution=(1080, 1920))
start99_g = Template(r"tpl1744989990233.png", record_pos=(-0.112, 0.606), resolution=(1080, 1920))
startNex99_G = Template(r"tpl1744990082322.png", record_pos=(0.006, 0.686), resolution=(1080, 1920))
########################################

def login_farm1():

    try:

        if exists(in_kal3ah) or exists(out_kal3ah):
            touch((88, 60))
            wait(e3dadat)
            touch(e3dadat)
            wait(alhesab)
            touch(alhesab)
            wait(t3eeralhesab)
            touch(t3eeralhesab)
            wait(top_t3eeralhesab)
            touch(inemail)
            wait(actvate_kepord)
            #####

            text(records[0][1],False)
            
            #text('<EMAIL>')

            if exists(next_1):
                touch(next_1)
                if exists(lakdda5ltalhesab):
                    touch(backdo5ol)
                    wait(t3eeralhesab)
                    touch(backdo5ol)
                    wait(alhesab)
                    touch(back_fede_shafaf)
                    touch(back_fede_shafaf) 
                else:

                    text(records[0][2])
                    #text('1600acer',False)

                    wait(tsjel_ald5ol)
                    touch(tsjel_ald5ol)
                    wait(start99_g,timeout=100)
                    touch(startNex99_G)
                    wait(in_kal3ah,timeout=100)


    except:
        print('axcept log 1')
        #youm_alhath()
def login_farm2():

    try:

        if exists(in_kal3ah) or exists(out_kal3ah):
            touch((88, 60))
            wait(e3dadat)
            touch(e3dadat)
            wait(alhesab)
            touch(alhesab)
            wait(t3eeralhesab)
            touch(t3eeralhesab)
            wait(top_t3eeralhesab)
            touch(inemail)
            wait(actvate_kepord)
            #####

            text(records[1][1])

            #text('<EMAIL>')
            wait(next_1)
            if exists(next_1):
                touch(next_1)
                if exists(lakdda5ltalhesab):
                    touch(backdo5ol)
                    wait(t3eeralhesab)
                    touch(backdo5ol)
                    wait(alhesab)
                    touch(back_fede_shafaf)
                    touch(back_fede_shafaf) 
                else:
                    wait(ente_password)

                    text(records[1][2])
                    
                    wait(tsjel_ald5ol)
                    touch(tsjel_ald5ol)
                    wait(start99_g,timeout=100)
                    touch(startNex99_G)
                    wait(in_kal3ah,timeout=100)
                    
                    
    except:
        print('except log 2')
        youm_alhath()
def login_farm3():

    try:
        if exists(in_kal3ah) or exists(out_kal3ah):
            touch((88, 60))
            wait(e3dadat)
            touch(e3dadat)
            wait(alhesab)
            touch(alhesab)
            wait(t3eeralhesab)
            touch(t3eeralhesab)
            wait(top_t3eeralhesab)
            touch(inemail)
            wait(actvate_kepord)
            #####

            text(records[2][1])
            
            #text('<EMAIL>')
            wait(next_1)
            if exists(next_1):
                touch(next_1)
                if exists(lakdda5ltalhesab):
                    touch(backdo5ol)
                    wait(t3eeralhesab)
                    touch(backdo5ol)
                    wait(alhesab)
                    touch(back_fede_shafaf)
                    touch(back_fede_shafaf) 
                else:
                    wait(ente_password)
                    
                    text(records[2][2])
                    
                    wait(tsjel_ald5ol)
                    touch(tsjel_ald5ol)
                    wait(start99_g,timeout=100)
                    touch(startNex99_G)
                    wait(in_kal3ah,timeout=100)
                    
    except:
        print('axcept log3')
        youm_alhath()
def login_farm4():

    try:
        if exists(in_kal3ah) or exists(out_kal3ah):
            touch((88, 60))
            wait(e3dadat)
            touch(e3dadat)
            wait(alhesab)
            touch(alhesab)
            wait(t3eeralhesab)
            touch(t3eeralhesab)
            wait(top_t3eeralhesab)
            touch(inemail)
            wait(actvate_kepord)
            #####

            text(records[3][1])
            
            #text('<EMAIL>')
            wait(next_1)
            if exists(next_1):
                touch(next_1)
                if exists(lakdda5ltalhesab):
                    touch(backdo5ol)
                    wait(t3eeralhesab)
                    touch(backdo5ol)
                    wait(alhesab)
                    touch(back_fede_shafaf)
                    touch(back_fede_shafaf) 
                else:
                    wait(ente_password)
                    
                    text(records[3][2])
                    
                    wait(tsjel_ald5ol)
                    touch(tsjel_ald5ol)
                    wait(start99_g,timeout=100)
                    touch(startNex99_G)
                    wait(in_kal3ah,timeout=100)
                   

    except:
        print('axcept log4')
        youm_alhath()






