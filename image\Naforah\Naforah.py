# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
###اضغط على الصورة
obj = Template(r"tpl1656584115476.png", threshold=0.95, record_pos=(0.119, 0.085), resolution=(1080, 1920))
### حتى يتلاشا
obj2 = Template(r"tpl1685682746173.png", record_pos=(-0.122, 0.674), resolution=(1080, 1920))

def fedi(obj, obj2, timeout = 60):
    for t in range(timeout):
        sleep(1)
        if (not exists(obj)): return 1
        elif t == timeout - 1:
            print("timeout")
            sys.exit()
        elif (exists(obj2)): touch(obj2)



def Naforah():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        open_menu_to_o5ra()
        if exists(Template(r"tpl1656583655195.png", threshold=0.95, target_pos=4, record_pos=(0.066, 0.365), resolution=(1080, 1920))):
            touch(Template(r"tpl1656583655195.png", threshold=0.95, target_pos=4, record_pos=(0.066, 0.365), resolution=(1080, 1920)))
            sleep()
            touch((555, 928))
            sleep()
            if exists(Template(r"tpl1656583776618.png", threshold=0.95, record_pos=(-0.106, 0.044), resolution=(1080, 1920))):
                touch(Template(r"tpl1656583776618.png", threshold=0.95, record_pos=(-0.106, 0.044), resolution=(1080, 1920)))
                wait(Template(r"tpl1656583822071.png", threshold=0.95, record_pos=(0.002, -0.842), resolution=(1080, 1920)),timeout=8)
                fedi(obj, obj2, timeout = 60)
                if exists(Template(r"tpl1668777724004.png", record_pos=(-0.036, 0.14), resolution=(1080, 1920))):
                    touch([515, 508])
                wait(Template(r"tpl1656583822071.png", threshold=0.95, record_pos=(0.002, -0.842), resolution=(1080, 1920)),timeout=8)
                touch(back_shahen)
                
        else:
            close_menu()
    except:
        print('axcept Nafora')
        youm_alhath()



# script content



# generate html report
# from airtest.report.report import simple_report
# simple_report(__file__, logpath=None)
