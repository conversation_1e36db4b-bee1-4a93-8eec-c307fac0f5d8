<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>675</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>675</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>675</height>
   </size>
  </property>
  <property name="sizeIncrement">
   <size>
    <width>400</width>
    <height>675</height>
   </size>
  </property>
  <property name="baseSize">
   <size>
    <width>400</width>
    <height>675</height>
   </size>
  </property>
  <property name="mouseTracking">
   <bool>false</bool>
  </property>
  <property name="tabletTracking">
   <bool>false</bool>
  </property>
  <property name="focusPolicy">
   <enum>Qt::NoFocus</enum>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <widget class="QTabWidget" name="tabWidget">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>4</y>
     <width>400</width>
     <height>631</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>400</width>
     <height>0</height>
    </size>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="toolTip">
    <string/>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <property name="usesScrollButtons">
    <bool>true</bool>
   </property>
   <widget class="QWidget" name="tab">
    <attribute name="title">
     <string>المهمات</string>
    </attribute>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>10</y>
       <width>231</width>
       <height>61</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:9pt;&quot;&gt;&lt;br/&gt;&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="lineWidth">
      <number>2</number>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:10pt; font-weight:600;&quot;&gt;farm&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
    </widget>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>120</y>
       <width>371</width>
       <height>61</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="layoutDirection">
      <enum>Qt::RightToLeft</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="locale">
      <locale language="Arabic" country="PalestinianTerritories"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;TextLabel &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="textFormat">
      <enum>Qt::MarkdownText</enum>
     </property>
    </widget>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>40</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="lineWidth">
      <number>2</number>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;انواع الاحجار&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
    </widget>
    <widget class="QWidget" name="gridLayoutWidget_2">
     <property name="geometry">
      <rect>
       <x>220</x>
       <y>200</y>
       <width>160</width>
       <height>130</height>
      </rect>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="2" column="0">
       <widget class="QCheckBox" name="checkBox_6">
        <property name="text">
         <string>Prince skill</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QCheckBox" name="checkBox_4">
        <property name="toolTip">
         <string>Free Hall of Champions</string>
        </property>
        <property name="text">
         <string>Recruit free heroes</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QCheckBox" name="checkBox_5">
        <property name="toolTip">
         <string>Material lab</string>
        </property>
        <property name="text">
         <string>gemstone studs</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QCheckBox" name="checkBox_8">
        <property name="toolTip">
         <string>Send to fields</string>
        </property>
        <property name="text">
         <string>Send to fields</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QCheckBox" name="checkBox_10">
        <property name="toolTip">
         <string>Daily Points Boxes</string>
        </property>
        <property name="text">
         <string>Open score boxes</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="gridLayoutWidget">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>200</y>
       <width>194</width>
       <height>159</height>
      </rect>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="4" column="0">
       <widget class="QCheckBox" name="checkBox_11">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="toolTip">
         <string>collection speed</string>
        </property>
        <property name="text">
         <string>collection speed</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QCheckBox" name="checkBox">
        <property name="font">
         <font>
          <pointsize>9</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="acceptDrops">
         <bool>false</bool>
        </property>
        <property name="toolTip">
         <string>Dragon Challenge+Free Mystery Tower+monthly signature</string>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Daily tasks</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <property name="autoRepeat">
         <bool>false</bool>
        </property>
        <property name="autoExclusive">
         <bool>false</bool>
        </property>
        <property name="tristate">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QCheckBox" name="checkBox_3">
        <property name="toolTip">
         <string>elf gift</string>
        </property>
        <property name="text">
         <string>elf gift</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QCheckBox" name="checkBox_7">
        <property name="toolTip">
         <string>Magic Fountain Silver Free Choice</string>
        </property>
        <property name="text">
         <string>Magic Fountain</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QCheckBox" name="checkBox_2">
        <property name="toolTip">
         <string>port gift</string>
        </property>
        <property name="text">
         <string>Daily fund opening</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QCheckBox" name="checkBox_12">
        <property name="toolTip">
         <string>Perks of Monster Invasion Survey Tower</string>
        </property>
        <property name="text">
         <string>Survey tower gifts</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QLabel" name="label_4">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>10</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="lineWidth">
      <number>2</number>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;نوع الحقل&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
    </widget>
    <widget class="QPushButton" name="start_bot">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>560</y>
       <width>111</width>
       <height>28</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>تشغيل البرنامج</string>
     </property>
     <property name="text">
      <string>Start All Farm</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="checkBox_13">
     <property name="geometry">
      <rect>
       <x>220</x>
       <y>330</y>
       <width>161</width>
       <height>21</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>Farm protection 3 days</string>
     </property>
     <property name="text">
      <string>protection 3 Day</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_5">
     <property name="geometry">
      <rect>
       <x>140</x>
       <y>70</y>
       <width>51</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="layoutDirection">
      <enum>Qt::RightToLeft</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="locale">
      <locale language="Arabic" country="PalestinianTerritories"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;TextLabel &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="textFormat">
      <enum>Qt::MarkdownText</enum>
     </property>
    </widget>
    <widget class="QLabel" name="label_6">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>70</y>
       <width>131</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="layoutDirection">
      <enum>Qt::RightToLeft</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="locale">
      <locale language="Arabic" country="PalestinianTerritories"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Sunken</enum>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Total Farm ID&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="textFormat">
      <enum>Qt::MarkdownText</enum>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_2">
     <property name="geometry">
      <rect>
       <x>250</x>
       <y>500</y>
       <width>141</width>
       <height>31</height>
      </rect>
     </property>
     <property name="text">
      <string>Start defind farm3D</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>320</x>
       <y>410</y>
       <width>71</width>
       <height>20</height>
      </rect>
     </property>
     <property name="text">
      <string>ID the farm</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit">
     <property name="geometry">
      <rect>
       <x>320</x>
       <y>430</y>
       <width>71</width>
       <height>22</height>
      </rect>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton">
     <property name="geometry">
      <rect>
       <x>310</x>
       <y>460</y>
       <width>81</width>
       <height>28</height>
      </rect>
     </property>
     <property name="text">
      <string>go to Farm</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_8">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>380</y>
       <width>231</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:400;&quot;&gt;Event Center&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
    </widget>
    <widget class="QWidget" name="horizontalLayoutWidget">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>400</y>
       <width>97</width>
       <height>22</height>
      </rect>
     </property>
     <layout class="QFormLayout" name="formLayout">
      <item row="0" column="0">
       <widget class="QCheckBox" name="checkBox_15">
        <property name="text">
         <string>Catapult</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QCheckBox" name="checkBox_16">
     <property name="geometry">
      <rect>
       <x>220</x>
       <y>350</y>
       <width>161</width>
       <height>20</height>
      </rect>
     </property>
     <property name="text">
      <string>collection IN Fild</string>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="tab_2">
    <attribute name="title">
     <string>Tools</string>
    </attribute>
    <widget class="QPushButton" name="StartTools_pushButton">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>670</y>
       <width>93</width>
       <height>28</height>
      </rect>
     </property>
     <property name="text">
      <string>Start</string>
     </property>
    </widget>
    <widget class="QPushButton" name="stop_Tool_pushButton">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>670</y>
       <width>93</width>
       <height>28</height>
      </rect>
     </property>
     <property name="text">
      <string>stop</string>
     </property>
    </widget>
    <widget class="QPushButton" name="add_farm_m2_pushButton">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>300</x>
       <y>570</y>
       <width>93</width>
       <height>28</height>
      </rect>
     </property>
     <property name="text">
      <string>Add Farm</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="checkBox_14">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>30</y>
       <width>241</width>
       <height>20</height>
      </rect>
     </property>
     <property name="text">
      <string>Open the jewelry from the bag</string>
     </property>
    </widget>
    <widget class="QPushButton" name="StartTools_pushButton_2">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>570</y>
       <width>171</width>
       <height>28</height>
      </rect>
     </property>
     <property name="text">
      <string>Start Task 1 Farm</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="checkBox_9">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>10</y>
       <width>141</width>
       <height>20</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>Magic King Corps</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QPushButton" name="exit_bot">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>640</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="toolTip">
    <string extracomment="-1"/>
   </property>
   <property name="toolTipDuration">
    <number>5</number>
   </property>
   <property name="statusTip">
    <string/>
   </property>
   <property name="whatsThis">
    <string/>
   </property>
   <property name="text">
    <string>Stop &amp; close</string>
   </property>
   <property name="autoDefault">
    <bool>false</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
