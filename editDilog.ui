<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Edit_D</class>
 <widget class="QDialog" name="Edit_D">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>351</width>
    <height>525</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>351</width>
    <height>440</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>351</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QPushButton" name="Edit_Add_pushButton">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>380</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>Add</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="Edit_Password_lineEdit_2">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>170</y>
     <width>201</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="edit_Usename_lineEdit">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>130</y>
     <width>201</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>10</y>
     <width>351</width>
     <height>61</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt; font-weight:600;&quot;&gt;Edit Farm&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QComboBox" name="Edit_Taib_Fild_comboBox">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>213</y>
     <width>199</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Taip Fild</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>Wheat</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Wood</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Iron</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Silvar</string>
    </property>
   </item>
  </widget>
  <widget class="QComboBox" name="Edit_Taib_ston_comboBox">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>324</y>
     <width>199</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Fusing gear type</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>worm stone</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>upgrade stone</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>strengthening stone</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>purification stone</string>
    </property>
   </item>
  </widget>
  <widget class="QComboBox" name="Edit_Lvl_Fild_comboBox">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>287</y>
     <width>199</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>field level</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>1</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>2</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>3</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>4</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>5</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>6</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>7</string>
    </property>
   </item>
  </widget>
  <widget class="QComboBox" name="Edit_Number_Marsh_comboBox">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>250</y>
     <width>199</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>The number of legion for farms</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>1</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>2</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>3</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>4</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>5</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>6</string>
    </property>
   </item>
  </widget>
  <widget class="QPushButton" name="Back_Edit_pushButton">
   <property name="geometry">
    <rect>
     <x>70</x>
     <y>380</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Back </string>
   </property>
   <property name="text">
    <string>Back</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="ID_lineEdit">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>90</y>
     <width>201</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>85</y>
     <width>86</width>
     <height>261</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLabel" name="label_8">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;ID&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;User Name&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;password&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_3">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Taib Fild&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_4">
      <property name="text">
       <string>Number Marsh</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_5">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Lvl Fild&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_6">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Taib of Ston&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
