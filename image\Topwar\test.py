#!/usr/bin/env python
# -*- coding: utf-8 -*-
from airtest.core.win import *
from airtest.cli.parser import cli_setup

import os
import sys
import random
import threading
import time



from pywinauto.findwindows import find_elements



def main():
    # the Chinese charactors "计算器" means "Calculator", you can replace it with English title:
    element_list = find_elements(title_re=".*Age of Origins.*")
    # print each of the handle of the "计算器" windows:
    for element in element_list:
        print(element.handle)
    
#connect_device("Windows:///")        
        

if __name__ == '__main__':
    main()
