<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>853</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>853</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>856</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>BOOT RISE OF THE KINGS</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>221</width>
      <height>61</height>
     </rect>
    </property>
    <property name="toolTip">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:9pt;&quot;&gt;&lt;br/&gt;&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Box</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Sunken</enum>
    </property>
    <property name="lineWidth">
     <number>2</number>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:10pt; font-weight:600;&quot;&gt;farm&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_3">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>80</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::Box</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Sunken</enum>
    </property>
    <property name="lineWidth">
     <number>2</number>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;انواع الاحجار&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>120</y>
      <width>371</width>
      <height>61</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>10</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::RightToLeft</enum>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
    <property name="locale">
     <locale language="Arabic" country="PalestinianTerritories"/>
    </property>
    <property name="frameShape">
     <enum>QFrame::Box</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Sunken</enum>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;TextLabel &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
    <property name="textFormat">
     <enum>Qt::MarkdownText</enum>
    </property>
   </widget>
   <widget class="QWidget" name="gridLayoutWidget_2">
    <property name="geometry">
     <rect>
      <x>230</x>
      <y>210</y>
      <width>160</width>
      <height>103</height>
     </rect>
    </property>
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="1" column="0">
      <widget class="QCheckBox" name="checkBox_5">
       <property name="text">
        <string>ترصيع الاحجار</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QCheckBox" name="checkBox_6">
       <property name="text">
        <string>مهارة امير</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QCheckBox" name="checkBox_4">
       <property name="text">
        <string>تجنيد ابطال</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QCheckBox" name="checkBox_8">
       <property name="text">
        <string>ارسال الى الحقول</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="gridLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>200</y>
      <width>151</width>
      <height>105</height>
     </rect>
    </property>
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QCheckBox" name="checkBox">
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="acceptDrops">
        <bool>false</bool>
       </property>
       <property name="toolTip">
        <string/>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="text">
        <string>المهام اليومية</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
       <property name="autoRepeat">
        <bool>false</bool>
       </property>
       <property name="autoExclusive">
        <bool>false</bool>
       </property>
       <property name="tristate">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QCheckBox" name="checkBox_2">
       <property name="text">
        <string>فتح الصندوق اليومي</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QCheckBox" name="checkBox_3">
       <property name="text">
        <string>هبة العفريت </string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QCheckBox" name="checkBox_7">
       <property name="text">
        <string>النافورة السحرية</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QPushButton" name="start_bot">
    <property name="geometry">
     <rect>
      <x>80</x>
      <y>630</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="toolTip">
     <string>تشغيل البرنامج</string>
    </property>
    <property name="text">
     <string>start</string>
    </property>
   </widget>
   <widget class="QPushButton" name="exit_bot">
    <property name="geometry">
     <rect>
      <x>190</x>
      <y>630</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="toolTip">
     <string extracomment="-1"/>
    </property>
    <property name="toolTipDuration">
     <number>5</number>
    </property>
    <property name="statusTip">
     <string/>
    </property>
    <property name="whatsThis">
     <string/>
    </property>
    <property name="text">
     <string>exit</string>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_4">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>80</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::Box</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Sunken</enum>
    </property>
    <property name="lineWidth">
     <number>2</number>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;نوع الحقل&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>400</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
