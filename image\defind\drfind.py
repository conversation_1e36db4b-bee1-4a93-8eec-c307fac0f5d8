# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time


#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
emailB = Template(r"tpl1667558284914.png", record_pos=(-0.43, 0.834), resolution=(1080, 1920))
emailTop = Template(r"tpl1667558416125.png", record_pos=(0.003, -0.847), resolution=(1080, 1920))

alnetham = Template(r"tpl1667558494244.png", record_pos=(-0.156, 0.256), resolution=(1080, 1920))
alnethamTop = Template(r"tpl1667560346140.png", record_pos=(0.003, -0.845), resolution=(1080, 1920))
mabrok = Template(r"tpl1667560455002.png", record_pos=(-0.003, -0.244), resolution=(1080, 1920))

def defind():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        try:
            wait(in_kal3ah, timeout=90)
        except:
            aout_2_in()

        if exists(emailB):
            touch(emailB)
            try:
                wait(emailTop, timeout=3)
            except:
                print("no hav go masera") 
            touch(alnetham)
            try:
                wait(alnethamTop, timeout=3)
            except:
                print("no hav go masera")
            touch(Template(r"tpl1667560395367.png", record_pos=(0.0, 0.816), resolution=(1080, 1920)))
            try:
                wait(mabrok, timeout=3)
            except:
                print("no hav go masera")
            if exists(mabrok):
                touch([566, 1361])
                try:
                    wait(alnethamTop, timeout=3)
                except:
                    print("no hav go masera")
                touch(back_shahen)
                try:
                    wait(emailTop, timeout=3)
                except:
                    print("no hav go masera")
                touch(back_shahen)
            else:
                touch(back_shahen)
                try:
                    wait(emailTop, timeout=3)
                except:
                    print("no hav go masera")
                touch(back_shahen)
                       
        if exists(open_menu):
            touch(open_menu)
            wait(nathra)
            touch([837, 473])
            swipe([662, 1382], [662, 662])
            try:
                wait(Template(r"tpl1666366276419.png", threshold=0.85, target_pos=4, record_pos=(0.087, 0.521), resolution=(1080, 1920)),timeout=5)
            except:
                pass
            touch(Template(r"tpl1666366276419.png", threshold=0.85, target_pos=4, record_pos=(0.087, 0.521), resolution=(1080, 1920)))
            try:
                wait(Template(r"tpl1666366321056.png", record_pos=(0.0, -0.854), resolution=(1080, 1920)), timeout=5)
            except:
                pass
            if  exists(Template(r"tpl1666366369042.png", threshold=0.95, record_pos=(0.193, -0.594), resolution=(1080, 1920))):
                touch(Template(r"tpl1666366369042.png", threshold=0.95, record_pos=(0.193, -0.594), resolution=(1080, 1920)))
                try:
                    wait(Template(r"tpl1666366400658.png", record_pos=(0.001, -0.85), resolution=(1080, 1920)),timeout=5)
                except:
                    pass
                if not exists(Template(r"tpl1668618564191.png", threshold=0.95, record_pos=(0.428, -0.43), resolution=(1080, 1920))):
                     if exists(Template(r"tpl1666366466585.png", threshold=0.9, rgb=True, target_pos=7, record_pos=(-0.036, 0.313), resolution=(1080, 1920))):
                            touch(Template(r"tpl1666366466585.png", threshold=0.9, rgb=True, target_pos=7, record_pos=(-0.036, 0.313), resolution=(1080, 1920)))
                            sleep()
                            touch(back_shahen)
                            try:
                                wait(Template(r"tpl1666366321056.png", record_pos=(0.0, -0.854), resolution=(1080, 1920)), timeout=5)
                            except:
                                pass                
                            touch(back_shahen)
                            try:
                                wait(close_menu1,timeout=5)
                            except:
                                pass
                            touch(close_menu1)                   
                     elif exists(Template(r"tpl1667568025905.png", threshold=0.95, rgb=True, record_pos=(-0.32, 0.361), resolution=(1080, 1920))):
                        touch(Template(r"tpl1667568025905.png", threshold=0.95, rgb=True, record_pos=(-0.32, 0.361), resolution=(1080, 1920)))
                        try:
                            wait(Template(r"tpl1668618877971.png", record_pos=(0.004, 0.139), resolution=(1080, 1920)), timeout=4)
                        except:
                            pass
                        if exists(Template(r"tpl1668618877971.png", record_pos=(0.004, 0.139), resolution=(1080, 1920))):
                            touch(Template(r"tpl1668618877971.png", record_pos=(0.004, 0.139), resolution=(1080, 1920)))
                    
                        try:
                            wait(Template(r"tpl1668619224887.png", threshold=0.95, record_pos=(0.006, -0.844), resolution=(1080, 1920)), timeout=5)
                        except:
                            pass
                        sleep()
                        touch(back_shahen)  
                        try:
                            wait(Template(r"tpl1666366321056.png", record_pos=(0.0, -0.854), resolution=(1080, 1920)), timeout=4)
                        except:
                            pass
                        touch(back_shahen)
                        try:
                            wait(close_menu1,timeout=5)
                        except:
                            pass
                        touch(close_menu1)              

                else:
                    touch(back_shahen)

                    if exists(back_shahen):
                        touch(back_shahen)
                    try:
                        wait(close_menu1,timeout=5)
                    except:
                        pass
                    if exists(close_menu1):
                        touch(close_menu1)
            
        else:
            close_menu()            
        
    except:
        print('defind exept')
        youm_alhath()
