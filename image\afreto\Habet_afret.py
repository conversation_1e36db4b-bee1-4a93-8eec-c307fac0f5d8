# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:21513",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
def Habet_afret():
    try:
        if exists(back_shahen):
            touch(back_shahen)
        if exists(out_kal3ah):
            aout_2_in()
        wait(in_kal3ah, timeout=90)
        
               
        if exists(Template(r"tpl1656851882921.png", threshold=0.8, record_pos=(0.423, -0.847), resolution=(1080, 1920))):
            touch([823, 93])
            sleep()
            if not exists(Template(r"tpl1666771677375.png", threshold=0.8, record_pos=(-0.405, -0.656), resolution=(1080, 1920))):
                touch((97, 265))
                sleep()
                touch(back_fede_shafaf)
        
            else:
                touch(back_fede_shafaf)
                sleep()
        if exists(Template(r"tpl1656706866653.png", threshold=0.95, record_pos=(0.423, -0.847), resolution=(1080, 1920))):
            touch(Template(r"tpl1656706866653.png", threshold=0.95, record_pos=(0.423, -0.847), resolution=(1080, 1920)))
            sleep()
            
                           
        if exists(Template(r"tpl1656570669620.png", threshold=0.95, record_pos=(-0.124, -0.718), resolution=(1080, 1920))):
            
            touch(Template(r"tpl1656570669620.png", threshold=0.95, record_pos=(-0.124, -0.718), resolution=(1080, 1920)))
            sleep()
            swipe((974, 277), (235, 282))
            sleep()
            touch([986, 317])
            sleep()
            touch([958, 545])
            sleep()
            touch(back_shahen)
        else:
            touch(back_shahen)
            
    except:
        print('habet afret exept')
        youm_alhath()
