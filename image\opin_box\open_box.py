# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time

#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"

###################################################################################
using(r"image/close")
from close import *

x_loop = 1
def openbox():


    try:
        if exists(back_shahen):
            touch(back_shahen)
        wait(in_kal3ah, timeout=90)
        while x_loop == 1:
            if exists(Template(r"tpl1666117009083.png", threshold=0.95, record_pos=(0.054, 0.82), resolution=(1080, 1920))):
                touch((599, 1835))
                wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                if not exists(Template(r"tpl1666114318381.png", threshold=0.95, record_pos=(0.313, -0.571), resolution=(1080, 1920))):
                    touch([872, 312])
                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break
                if not exists(Template(r"tpl1666115512435.png", threshold=0.95, rgb=False, record_pos=(0.202, -0.57), resolution=(1080, 1920))):
                    touch([757, 310])
                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break

                if not exists(Template(r"tpl1666116033884.png", threshold=0.95, record_pos=(0.278, -0.355), resolution=(1080, 1920))):
                    touch([830, 550])
                    wait(Template(r"tpl1666115841882.png", threshold=0.95, record_pos=(0.022, -0.491), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920)))
                        wait(Template(r"tpl1666115841882.png", threshold=0.95, record_pos=(0.022, -0.491), resolution=(1080, 1920)))
                        touch([541, 1616])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1625])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666116244433.png", threshold=0.95, record_pos=(-0.008, 0.428), resolution=(1080, 1920))):
                        touch([538, 1632])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break         
                if not exists(Template(r"tpl1666116567529.png", threshold=0.95, record_pos=(0.076, -0.572), resolution=(1080, 1920))):
                    touch([622, 312])
                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break
                if not exists(Template(r"tpl1666117195497.png", threshold=0.95, record_pos=(-0.056, -0.572), resolution=(1080, 1920))):
                    touch([478, 310])

                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break               
                if not exists(Template(r"tpl1666117324409.png", threshold=0.95, record_pos=(-0.002, -0.356), resolution=(1080, 1920))):
                    touch([538, 545])
                    wait(Template(r"tpl1666117399200.png", threshold=0.95, record_pos=(0.032, -0.492), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920)))
                        wait(Template(r"tpl1666117399200.png", threshold=0.95, record_pos=(0.032, -0.492), resolution=(1080, 1920)))
                        touch([538, 1641])

                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1625])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666116244433.png", threshold=0.95, record_pos=(-0.008, 0.428), resolution=(1080, 1920))):
                        touch([538, 1632])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break           
                if not exists(Template(r"tpl1666119023486.png", threshold=0.95, record_pos=(-0.197, -0.572), resolution=(1080, 1920))):
                    touch([326, 312])
                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break                
                if not exists(Template(r"tpl1666117685850.png", threshold=0.95, record_pos=(-0.367, -0.57), resolution=(1080, 1920))):
                    touch([146, 319])
                    wait(Template(r"tpl1666114370504.png", record_pos=(0.002, -0.473), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666114529373.png", threshold=0.7, rgb=True, record_pos=(-0.001, 0.462), resolution=(1080, 1920)))
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1667])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114871580.png", threshold=0.95, rgb=False, record_pos=(0.0, 0.462), resolution=(1080, 1920))):
                        touch([548, 1655])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break                
                if not exists(Template(r"tpl1666117828394.png", threshold=0.95, rgb=False, record_pos=(-0.28, -0.359), resolution=(1080, 1920))):
                    touch([240, 559])
                    wait(Template(r"tpl1666117896896.png", threshold=0.95, record_pos=(0.036, -0.492), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920))):
                        touch(Template(r"tpl1666115880427.png", threshold=0.95, record_pos=(-0.005, 0.431), resolution=(1080, 1920)))
                        wait(Template(r"tpl1666117896896.png", threshold=0.95, record_pos=(0.036, -0.492), resolution=(1080, 1920)))
                        touch([536, 1646])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666114667957.png", threshold=0.95, record_pos=(0.005, 0.463), resolution=(1080, 1920))):
                        touch([543, 1625])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                    if exists(Template(r"tpl1666116244433.png", threshold=0.95, record_pos=(-0.008, 0.428), resolution=(1080, 1920))):
                        touch([538, 1632])
                        wait(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920)))
                        break                 

                else:
                    if exists(Template(r"tpl1665927844817.png", record_pos=(0.005, -0.845), resolution=(1080, 1920))):
                        touch([67, 48])
            if x_loop == 1:
                break                
                    
    except:
        print('except oben box')
        youm_alhath()        
