# -*- encoding=utf8 -*-
__author__ = "fofo"

from airtest.core.api import *
from airtest.cli.parser import cli_setup
import os
import sys
import random
import threading
import time
#if not cli_setup():
#auto_setup(__file__, logdir=None, devices=["Android:///",], project_root="C:/FarmTheRiseKing")
#auto_setup(__file__, logdir=None, devices=["android://127.0.0.1:5037/127.0.0.1:7555",], project_root="C:/bootGames")
ST.PROJECT_ROOT = "C:\\bootGames"
###################################################################################
using(r"image/close")
from close import *
################################################################################
def Tagned_Abtal():
    try:

        open_menu_forsan()
        if exists(Template(r"tpl1684655138396.png", record_pos=(-0.085, -0.485), resolution=(1080, 1920))):
            touch(Template(r"tpl1684655138396.png", record_pos=(-0.085, -0.485), resolution=(1080, 1920)))
            sleep()
            touch(Template(r"tpl1684653559068.png", record_pos=(0.031, -0.206), resolution=(1080, 1920)))
            wait(Template(r"tpl1684653678547.png", record_pos=(0.342, 0.803), resolution=(1080, 1920)))

            if exists(Template(r"tpl1684653678547.png", record_pos=(0.342, 0.803), resolution=(1080, 1920))):
                touch([902, 1751])
                wait(Template(r"tpl1684653725839.png", record_pos=(0.0, -0.844), resolution=(1080, 1920)))
                if exists(Template(r"tpl1684653760850.png", record_pos=(0.201, 0.462), resolution=(1080, 1920))):
                    touch(Template(r"tpl1684653760850.png", record_pos=(0.201, 0.462), resolution=(1080, 1920)))
                    wait(Template(r"tpl1684653807381.png", record_pos=(-0.002, -0.601), resolution=(1080, 1920)))
                    touch([925, 1469])
                    sleep()
                    touch([72, 41])
                else:
                    touch([79, 41])
                    sleep()
        
    except:
        print('axcept tajned abtal')
        youm_alhath()

