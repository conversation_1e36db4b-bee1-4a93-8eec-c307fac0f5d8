<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>mainFarm_D</class>
 <widget class="QDialog" name="mainFarm_D">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>900</width>
    <height>800</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>900</width>
    <height>800</height>
   </size>
  </property>
  <property name="sizeIncrement">
   <size>
    <width>900</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QPushButton" name="delet_pushButton_2">
   <property name="geometry">
    <rect>
     <x>800</x>
     <y>580</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>Delet</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Main_Edit_pushButton_3">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>700</x>
     <y>620</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>Edit</string>
   </property>
  </widget>
  <widget class="QTableWidget" name="tableWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>100</y>
     <width>881</width>
     <height>461</height>
    </rect>
   </property>
   <column>
    <property name="text">
     <string>ID</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>User Name</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>User Password</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>Taib Fild</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>Number Marsh</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>lvl Fild</string>
    </property>
   </column>
  </widget>
  <widget class="QLineEdit" name="search_lineEdit">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>50</y>
     <width>291</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>60</y>
     <width>55</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>Search</string>
   </property>
  </widget>
  <widget class="QLabel" name="Total_User_label_2">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>50</y>
     <width>171</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>Total user:</string>
   </property>
  </widget>
  <widget class="QPushButton" name="back_pushButton">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>10</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="text">
    <string>back</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="User_Name_lineEdit">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>620</y>
     <width>209</width>
     <height>24</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Password Your Farm</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>257</x>
     <y>600</y>
     <width>81</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;password&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="Password_lineEdit">
   <property name="geometry">
    <rect>
     <x>240</x>
     <y>620</y>
     <width>209</width>
     <height>24</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>User Name of Your Farm</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>597</y>
     <width>108</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;User Name&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QComboBox" name="Taib_Fild_comboBox">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>620</y>
     <width>209</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Taip Fild</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>Wheat</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Wood</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Iron</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>Silvar</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>460</x>
     <y>600</y>
     <width>81</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Taib Fild&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QComboBox" name="Number_Marsh_comboBox">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>680</y>
     <width>209</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>The number of legion for farms</string>
   </property>
   <property name="currentIndex">
    <number>-1</number>
   </property>
   <item>
    <property name="text">
     <string>1</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>2</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>3</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>4</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>5</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>6</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>660</y>
     <width>108</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Number Marsh&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QComboBox" name="Lvl_Fild_comboBox">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>680</y>
     <width>209</width>
     <height>22</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>field level</string>
   </property>
   <property name="currentIndex">
    <number>0</number>
   </property>
   <item>
    <property name="text">
     <string>1</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>2</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>3</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>4</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>5</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>6</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>7</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>8</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>9</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>10</string>
    </property>
   </item>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>240</x>
     <y>660</y>
     <width>108</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;field level&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
  <widget class="QPushButton" name="add_add1_pushButton">
   <property name="geometry">
    <rect>
     <x>700</x>
     <y>580</y>
     <width>93</width>
     <height>28</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="toolTip">
    <string>add Info For Farmer</string>
   </property>
   <property name="text">
    <string>Add</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_8">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>10</y>
     <width>351</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:9pt; font-weight:600;&quot;&gt;The Total You can Add Farm Onle 45 Farm&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
